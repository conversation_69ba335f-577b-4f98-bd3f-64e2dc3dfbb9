import { createAccessControl } from 'better-auth/plugins/access';

// Simplified role-based access control
// We keep the granular structure for better-auth compatibility
// but use it primarily for role identification
const statement = {
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['create', 'read', 'update'],
  'workout-participant': ['update-own', 'remove-own'], // Customer self-management permissions
} as const;

const ac = createAccessControl(statement);

// Admin role: Full access to everything
const adminRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['create', 'read', 'update'],
});

// Trainer role: Can manage workouts and customers, read packages and organization
const trainerRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['read'],
  organization: ['read'],
});

// Customer role: Can create workouts and manage their own participation
const customerRole = ac.newRole({
  workout: ['create', 'read'],
  customer: ['read'],
  organization: ['read'],
  'workout-participant': ['update-own', 'remove-own'],
});

export { adminRole, trainerRole, customerRole, ac };
