import { betterAuth, BetterAuthOptions, GenericEndpointContext } from 'better-auth';
import { nextCookies } from 'better-auth/next-js';
import { admin, organization, username } from 'better-auth/plugins';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/postgres-js';
import { eq, and } from 'drizzle-orm';
import postgres from 'postgres';
import * as schema from './schema';
import { ac, adminRole, trainerRole, customerRole } from './ac';

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/loolookids';
const sql = postgres(connectionString);
const db = drizzle(sql, { schema });

const options = {
  database: drizzleAdapter(db, {
    provider: 'pg',
    usePlural: true,
  }),
  emailAndPassword: {
    enabled: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // Cache duration in seconds
    },
  },
  user: {
    additionalFields: {
      role: {
        type: 'string',
        defaultValue: 'user',
      },
    },
  },
  plugins: [
    username(),
    admin({
      ac,
      roles: {
        admin: adminRole,
        trainer: trainerRole,
        customer: customerRole,
      },
      defaultRole: 'user',
      adminRoles: ['admin'],
    }),
    organization({
      creatorRole: 'owner',
      slugGenerator: (name: string) =>
        name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, ''),
      async sendInvitationEmail(data) {
        console.debug('🚨 TODO!! 🚨');

        // const inviteLink = `https://example.com/accept-invitation/${data.id}`;

        // sendOrganizationInvitation({
        //   email: data.email,
        //   invitedByUsername: data.inviter.user.name,
        //   invitedByEmail: data.inviter.user.email,
        //   teamName: data.organization.name,
        //   inviteLink,
        // });
      },
    }),
    nextCookies(),
  ],
  databaseHooks: {
    session: {
      create: {
        // find the first organization the user is a member of and set it as the active organization
        before: async (session) => {
          console.debug('🐝 create session');

          try {
            const organizations = await db
              .select({
                id: schema.organizations.id,
                name: schema.organizations.name,
              })
              .from(schema.organizations)
              .innerJoin(schema.members, eq(schema.members.organizationId, schema.organizations.id))
              .where(and(eq(schema.members.userId, session.userId)));

            // we should only have one or none
            // one if onboarded
            // none if not onboarded

            return {
              data: {
                ...session,
                activeOrganizationId: organizations?.[0]?.id,
              },
            };
          } catch (error) {
            console.error('❌ Error listing organizations:', error);

            return {
              data: session,
            };
          }
        },
      },
    },
    user: {
      create: {
        after: async (user, context) => {
          console.debug('🐝 after create user', user);

          // Get the organization slug from the request header
          const orgSlug = context?.request?.headers?.get('x-organization');
          console.debug('🐞 orgSlug', orgSlug);

          if (!orgSlug) {
            return;
          }

          // Find the organization by slug
          const [org] = await db.select().from(schema.organizations).where(eq(schema.organizations.slug, orgSlug));
          console.debug('🐞 org', org);

          if (!org) {
            return;
          }

          // Insert member record with customer role
          await db.insert(schema.members).values({
            id: crypto.randomUUID(),
            userId: user.id,
            organizationId: org.id,
            role: 'customer',
            createdAt: new Date(),
          });
        },
      },
    },
  },
  advanced: {
    database: {
      generateId: false,
    },
  },
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> = betterAuth(options);

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;

export { db };
