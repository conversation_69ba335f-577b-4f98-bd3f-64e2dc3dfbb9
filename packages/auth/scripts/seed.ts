import 'dotenv/config';

import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';
import * as schema from "../src/schema.js";
import { reset } from "drizzle-seed";
import { auth } from '../src/server.js';
import { AuthContext } from 'better-auth';

async function main() {
    // Setup DB connection
    const client = new Client({
        connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/loolookids',
    });
    await client.connect();
    const db = drizzle(client);

    // Reset DB
    await reset(db, schema);

    // Initialize Better Auth context
    const ctx: AuthContext = await auth.$context;

    // Seed organization
    const [organization] = await db.insert(schema.organizations).values({
        name: 'LooLooKids',
        slug: 'loolookids',
        logo: '',
        metadata: '{}',
    }).returning();

    // Seed admin user
    const adminUser = await ctx.internalAdapter.createUser({
        email: "<EMAIL>",
        name: "ck",
        role: "admin",
        emailVerified: true,
    });

    // Seed trainer
    const trainerUser = await ctx.internalAdapter.createUser({
        email: "<EMAIL>",
        name: "kkcy",
        role: "trainer",
        emailVerified: true,
    });

    // Seed customer
    const [customerUser1] = await db.insert(schema.users).values({
        username: 'ivylim',
        name: "Yeoh Kai Cheng",
        role: "user",
        emailVerified: true,
        email: '<EMAIL>'
    }).returning();

    const [customerUser2] = await db.insert(schema.users).values({
        username: 'snghoayfung',
        name: "Chin Yi Xuan",
        role: "user",
        emailVerified: true,
        email: '<EMAIL>'
    }).returning();

    const [customerUser3] = await db.insert(schema.users).values({
        username: 'jeniferlee',
        name: "Kaylee Ang",
        role: "user",
        emailVerified: true,
        email: '<EMAIL>'
    }).returning();

    // Create account for users
    await ctx.internalAdapter.createAccount({
        userId: adminUser.id,
        providerId: "credential",
        accountId: adminUser.id,
        password: await ctx.password.hash('abcd1234'),
    });

    await ctx.internalAdapter.createAccount({
        userId: trainerUser.id,
        providerId: "credential",
        accountId: trainerUser.id,
        password: await ctx.password.hash('abcd1234'),
    });

    if (customerUser1) {
        await ctx.internalAdapter.createAccount({
            userId: customerUser1.id,
            providerId: "credential",
            accountId: customerUser1.id,
            password: await ctx.password.hash('abcd1234'),
        });
    }

    if (customerUser2) {
        await ctx.internalAdapter.createAccount({
            userId: customerUser2.id,
            providerId: "credential",
            accountId: customerUser2.id,
            password: await ctx.password.hash('abcd1234'),
        });
    }

    if (customerUser3) {
        await ctx.internalAdapter.createAccount({
            userId: customerUser3.id,
            providerId: "credential",
            accountId: customerUser3.id,
            password: await ctx.password.hash('abcd1234'),
        });
    }

    if (organization) {
        // seed members
        await db.insert(schema.members).values({
            organizationId: organization.id,
            userId: adminUser.id,
            role: 'admin',
            parentName: null,
        });

        await db.insert(schema.members).values({
            organizationId: organization.id,
            userId: trainerUser.id,
            role: 'trainer',
            parentName: null,
        });

        if (customerUser1) {
            await db.insert(schema.members).values({
                organizationId: organization.id,
                userId: customerUser1.id,
                role: 'customer',
                parentName: 'Ivy Lim',
            });
        }

        if (customerUser2) {
            await db.insert(schema.members).values({
                organizationId: organization.id,
                userId: customerUser2.id,
                role: 'customer',
                parentName: 'Sng Hoay Fung',
            });
        }

        if (customerUser3) {
            await db.insert(schema.members).values({
                organizationId: organization.id,
                userId: customerUser3.id,
                role: 'customer',
                parentName: 'Jenifer Lee',
            });
        }

        // seed packages
        await db.insert(schema.packages).values({
            name: 'Package 1',
            description: 'Description 1',
            sessionCount: 10,
            price: '600',
            isActive: true,
            organizationId: organization.id,
        })

        await db.insert(schema.packages).values({
            name: 'Package 2',
            description: 'Description 2',
            sessionCount: 20,
            price: '900',
            isActive: true,
            organizationId: organization.id,
        })

        await db.insert(schema.packages).values({
            name: 'Trial',
            description: 'Trial package',
            sessionCount: 2,
            price: '100',
            isActive: true,
            organizationId: organization.id,
        })
    }

    console.log('Seed complete!');
    await client.end();
}

main().catch((err) => {
    console.error('Seeding error:', err);
    process.exit(1);
});