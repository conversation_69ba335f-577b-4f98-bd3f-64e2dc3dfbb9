{"name": "@workspace/auth", "version": "0.0.0", "type": "module", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "generate": "drizzle-kit generate", "migrate": "drizzle-kit migrate", "studio": "drizzle-kit studio", "seed": "tsx scripts/seed.ts"}, "dependencies": {"@daveyplate/better-auth-ui": "^2.0.12", "better-auth": "^1.2.12", "drizzle-orm": "^0.38.4", "postgres": "^3.4.7"}, "devDependencies": {"@types/pg": "^8.15.4", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "dotenv": "^17.2.1", "drizzle-kit": "^0.30.6", "drizzle-seed": "^0.3.1", "pg": "^8.16.3", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "exports": {".": "./src/index.ts", "./client": "./src/client.ts", "./server": "./src/server.ts", "./config": "./src/config.ts", "./schema": "./src/schema.ts", "./types": "./src/types.ts"}}