// import { NextRequest, NextResponse } from "next/server";

// export async function middleware(request: NextRequest) {
//   const pathname = request.nextUrl.pathname;

//   // Skip auth check for auth routes and public assets
//   if (
//     pathname.startsWith("/api/auth") ||
//     pathname.startsWith("/login") ||
//     pathname.startsWith("/register") ||
//     pathname.startsWith("/_next") ||
//     pathname.startsWith("/favicon")
//   ) {
//     return NextResponse.next();
//   }

//   // Check for session token in cookies
//   const sessionToken = request.cookies.get("better-auth.session-token");

//   if (!sessionToken) {
//     // Redirect to login if not authenticated
//     const loginUrl = new URL("/login", request.url);
//     return NextResponse.redirect(loginUrl);
//   }

//   return NextResponse.next();
// }

// export const config = {
//   matcher: [
//     /*
//      * Match all request paths except for the ones starting with:
//      * - api (API routes)
//      * - _next/static (static files)
//      * - _next/image (image optimization files)
//      * - favicon.ico (favicon file)
//      */
//     "/((?!api|_next/static|_next/image|favicon.ico).*)",
//   ],
// };

import { getSessionCookie } from '@workspace/auth/server';
import { type NextRequest, NextResponse } from 'next/server';
import { extractSubdomain } from './lib/organization';

const appUrl = new URL(process.env.NEXT_PUBLIC_WEB_URL!);
const ROOT_DOMAIN = appUrl.host;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const organizationSlug = process.env.ORGANIZATION_SLUG || extractSubdomain(request, ROOT_DOMAIN);

  // API routes: always add X-Organization header if subdomain exists
  if (pathname.startsWith('/api/')) {
    if (organizationSlug) {
      return NextResponse.next({
        headers: {
          'X-Organization': organizationSlug,
        },
      });
    }
    return NextResponse.next();
  }

  // Home page is not protected
  // Pass organization slug to home page
  if (pathname === '/') {
    return NextResponse.next({
      headers: {
        'X-Organization': organizationSlug || '',
      },
    });
  }

  // Protected routes: check session
  const sessionCookie = getSessionCookie(request);
  if (!sessionCookie) {
    const redirectTo = pathname + request.nextUrl.search;
    return NextResponse.redirect(new URL(`/auth/sign-in?redirectTo=${redirectTo}`, request.url));
  }

  // For authenticated users, redirect to home if they access auth pages
  if (sessionCookie && pathname.startsWith('/auth')) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/customers',
    '/admin',
    '/api/:path*',
  ],
};
