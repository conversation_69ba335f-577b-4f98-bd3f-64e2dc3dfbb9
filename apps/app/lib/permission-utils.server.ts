'use server';

import { auth } from '@workspace/auth/server';
import { getCachedUserRole } from './permission-utils';

/**
 * Server Action: Force refresh role for a user
 * Use this after role/organization changes
 */
export async function refreshUserPermissions(userId: string): Promise<void> {
  await getCachedUserRole(userId);
}

/**
 * Server Action: Manual role refresh for current user
 * Can be called from client components
 */
export async function refreshCurrentUserPermissions(): Promise<{ success: boolean; message?: string }> {
  try {
    const session = await auth.api.getSession({ headers: await import('next/headers').then((m) => m.headers()) });
    if (!session?.user?.id) {
      return { success: false, message: 'No authenticated user found' };
    }

    await getCachedUserRole(session.user.id);
    return { success: true, message: 'Role refreshed successfully' };
  } catch (error) {
    console.error('Failed to refresh role:', error);
    return { success: false, message: 'Failed to refresh role' };
  }
}
