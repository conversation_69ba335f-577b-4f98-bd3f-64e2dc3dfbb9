// Helper to redirect on unauthorized
export const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (res.status === 401) {
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/sign-in';

      // TODO
      // && !url.includes('/api/workouts')) {
      // Only redirect for authenticated routes, not public workout endpoints
    }
    throw new Error('Unauthorized');
  }
  if (!res.ok) throw new Error('Failed to fetch');
  return res.json();
};

// Public fetcher for unauthenticated access to workouts
export const publicFetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    if (res.status === 400) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Bad request');
    }

    throw new Error('Failed to fetch');
  }

  return res.json();
};
