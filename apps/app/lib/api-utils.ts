import { NextRequest, NextResponse } from 'next/server';
import { auth, db, organizations } from '@workspace/auth/server';
import { ZodError, ZodSchema } from 'zod';
import { type ErrorResponse } from './validations';
import { canAccessOrganization, isAdmin, isTrainerOrAdmin } from './permission-utils';
import { eq } from 'drizzle-orm';

// Helper to get authenticated user from request
export async function getAuthenticatedUser(request?: NextRequest, headers?: Headers) {
  if (!request && !headers) {
    return null;
  }

  try {
    const session = await auth.api.getSession({
      headers: request?.headers ?? headers!,
    });

    if (!session?.user) {
      return null;
    }

    return session;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

// Helper to validate request body with Zod schema
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; error: ErrorResponse }> {
  try {
    const body = await request.json();
    const validatedData = schema.parse(body);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          error: 'Validation Error',
          message: 'Invalid request data',
          details: error.flatten().fieldErrors,
        },
      };
    }
    return {
      success: false,
      error: {
        error: 'Parse Error',
        message: 'Invalid JSON in request body',
      },
    };
  }
}

// Helper to validate query parameters with Zod schema
export function validateQueryParams<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): { success: true; data: T } | { success: false; error: ErrorResponse } {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    const validatedData = schema.parse(params);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          error: 'Validation Error',
          message: 'Invalid query parameters',
          details: error.flatten().fieldErrors,
        },
      };
    }
    return {
      success: false,
      error: {
        error: 'Parse Error',
        message: 'Invalid query parameters',
      },
    };
  }
}

// Helper to create error responses
export function createErrorResponse(
  error: string,
  message: string,
  status: number = 400,
  details?: Record<string, any>
): NextResponse<ErrorResponse> {
  const errorResponse: ErrorResponse = {
    error,
    message,
    ...(details && { details }),
  };

  return NextResponse.json(errorResponse, { status });
}

// Helper to create success responses
export function createSuccessResponse<T>(data: T, status: number = 200): NextResponse<T> {
  return NextResponse.json(data, { status });
}

// Helper to handle API route errors
export function handleApiError(error: unknown): NextResponse<ErrorResponse> {
  console.error('API Error:', error);

  if (error instanceof Error) {
    return createErrorResponse('Internal Server Error', error.message, 500);
  }

  return createErrorResponse('Internal Server Error', 'An unexpected error occurred', 500);
}

// Middleware wrapper for API routes with authentication
export function withAuth<T extends any[]>(
  handler: (
    request: NextRequest,
    userSession: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const userSession = await getAuthenticatedUser(request);

      if (!userSession?.user) {
        return createErrorResponse('Unauthorized', 'Authentication required', 401);
      }

      return await handler(request, userSession, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Middleware wrapper for API routes requiring admin permissions
export function withAdminAuth<T extends any[]>(
  handler: (
    request: NextRequest,
    userSession: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const userSession = await getAuthenticatedUser(request);

      if (!userSession?.user) {
        return createErrorResponse('Unauthorized', 'Authentication required', 401);
      }

      // Check admin role
      const hasAdminRole = await isAdmin(userSession.user.id);
      if (!hasAdminRole) {
        return createErrorResponse('Forbidden', 'Admin role required', 403);
      }

      return await handler(request, userSession, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Middleware wrapper for API routes requiring trainer-level permissions
export function withTrainerAuth<T extends any[]>(
  handler: (
    request: NextRequest,
    userSession: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const userSession = await getAuthenticatedUser(request);

      if (!userSession?.user) {
        return createErrorResponse('Unauthorized', 'Authentication required', 401);
      }

      // Check organization access and trainer role
      if (!(await canAccessOrganization(userSession.user.id))) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Check trainer or admin role
      const hasTrainerRole = await isTrainerOrAdmin(userSession.user.id);
      if (!hasTrainerRole) {
        return createErrorResponse('Forbidden', 'Trainer or admin role required', 403);
      }

      return await handler(request, userSession, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Middleware wrapper for API routes requiring authenticated users (customers or trainers)
export function withCustomerAuth<T extends any[]>(
  handler: (
    request: NextRequest,
    userSession: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const userSession = await getAuthenticatedUser(request);

      if (!userSession?.user) {
        return createErrorResponse('Unauthorized', 'Authentication required', 401);
      }

      // Check organization access - customers must be members of the organization
      if (!(await canAccessOrganization(userSession.user.id))) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      return await handler(request, userSession, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Middleware wrapper for public access - allows unauthenticated users with organization scope
export function withPublicAccess<T extends any[]>(
  handler: (
    request: NextRequest,
    organizationId: string,
    userSession?: Awaited<ReturnType<typeof getAuthenticatedUser>>,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Extract organization from subdomain or env via middleware header
      const organizationSlug = request.headers.get('x-organization') ?? '';

      // find organizationId from slug
      const [organization] = await db.select().from(organizations).where(eq(organizations.slug, organizationSlug));
      const organizationId = organization?.id;

      if (!organizationId) {
        return createErrorResponse('Bad Request', 'Organization not found', 400);
      }

      // Try to get authenticated user (optional for public routes)
      const userSession = await getAuthenticatedUser(request);

      return await handler(request, organizationId, userSession, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}
