import { auth } from '@workspace/auth/server';
import { cookies } from 'next/headers';

export const ROLE_CACHE_NAME = 'user_role_cache';
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

export type UserRole = 'admin' | 'trainer' | 'customer';

interface RoleCache {
  role: UserRole | null;
  userId: string;
  expiresAt: number;
  lastUpdated: number;
}

export function getNowEpoch() {
  return Math.floor(Date.now() / 1000);
}

/**
 * For use in layouts/server components: Only reads the role cookie.
 * If missing/expired, fetches fresh role but does NOT set the cookie.
 */
export async function getUserRoleForLayout(userId: string): Promise<UserRole | null> {
  const cookieStore = await cookies();
  const cachedRole = cookieStore.get(ROLE_CACHE_NAME);

  if (cachedRole) {
    try {
      const cached: RoleCache = JSON.parse(cachedRole.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        return cached.role;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }
  // Fallback: fetch fresh, but do NOT set cookie
  return await fetchUserRole(userId);
}

/**
 * Get cached permissions for a user, or fetch and cache if missing/expired.
 * Only use in server actions or API routes (can set cookies).
 */
export async function getCachedUserRole(userId: string): Promise<UserRole | null> {
  const cookieStore = await cookies();
  const cachedRole = cookieStore.get(ROLE_CACHE_NAME);

  if (cachedRole) {
    console.debug('🐞 cachedRole found');

    try {
      const cached: RoleCache = JSON.parse(cachedRole.value);
      if (cached.userId === userId && cached.expiresAt > Date.now()) {
        console.debug('🐞 cachedRole still valid');
        return cached.role;
      }
    } catch {
      // Ignore parse errors, fall through to fetch
    }
  }

  console.debug('🐞 cachedRole miss or expired');
  // Cache miss or expired: fetch fresh role and update cookie
  const role = await fetchUserRole(userId);
  console.debug('🐞 Role fetched:', role);

  try {
    cookieStore.set(
      ROLE_CACHE_NAME,
      JSON.stringify({
        role,
        userId,
        expiresAt: Date.now() + CACHE_DURATION,
        lastUpdated: Date.now(),
      }),
      {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: CACHE_DURATION / 1000,
      }
    );
  } catch (error) {
    // if this is run under client component, we ignore it
    console.debug('❌ Error setting user role cookie:', error);
  }

  return role;
}

/**
 * Fetch user role from auth service (no cache)
 */
export async function fetchUserRole(userId: string): Promise<UserRole | null> {
  try {
    // Check admin role first (highest privilege)
    const adminResult = await auth.api.userHasPermission({
      body: {
        userId,
        permissions: {
          financial: ['create', 'read', 'update', 'delete'],
          organization: ['create', 'read', 'update'],
        },
      },
    });

    if (adminResult.success) {
      return 'admin';
    }

    // Check trainer role (medium privilege)
    const trainerResult = await auth.api.userHasPermission({
      body: {
        userId,
        permissions: {
          workout: ['create', 'read', 'update', 'delete'],
          customer: ['create', 'read', 'update', 'delete'],
        },
      },
    });

    if (trainerResult.success) {
      return 'trainer';
    }

    // Default to customer role
    return 'customer';
  } catch (error) {
    console.error('❌ Error fetching user role:', error);
    return null;
  }
}

// Role-based permission checking functions

/**
 * Check if user has admin role
 */
export async function isAdmin(userId: string): Promise<boolean> {
  const role = await getCachedUserRole(userId);
  return role === 'admin';
}

/**
 * Check if user has trainer role or higher
 */
export async function isTrainerOrAdmin(userId: string): Promise<boolean> {
  const role = await getCachedUserRole(userId);
  return role === 'admin' || role === 'trainer';
}

/**
 * Check if user can access financial features
 */
export async function canAccessFinancial(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can manage packages
 */
export async function canManagePackages(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can read packages
 */
export async function canReadPackages(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage organization
 */
export async function canManageOrganization(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * Check if user can access organization
 */
export async function canAccessOrganization(userId: string): Promise<boolean> {
  // all users can access organization
  return true;
  // return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage customers
 */
export async function canManageCustomers(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user can manage workouts
 */
export async function canManageWorkouts(userId: string): Promise<boolean> {
  return await isTrainerOrAdmin(userId);
}

/**
 * Check if user is a customer
 */
export async function isCustomer(userId: string): Promise<boolean> {
  const role = await getCachedUserRole(userId);
  return role === 'customer';
}

/**
 * Get user's member ID in the active organization
 */
export async function getUserMemberId(userId: string, organizationId: string): Promise<string | null> {
  try {
    const { db, members } = await import('@workspace/auth/server');
    const { eq, and } = await import('drizzle-orm');

    const [member] = await db
      .select({ id: members.id })
      .from(members)
      .where(and(eq(members.userId, userId), eq(members.organizationId, organizationId)));

    return member?.id || null;
  } catch (error) {
    console.error('❌ Error getting user member ID:', error);
    return null;
  }
}

/**
 * Check if user can remove themselves from a workout (24-hour rule)
 */
export async function canCustomerRemoveFromWorkout(
  userId: string,
  workoutId: string,
  participantId: string,
  organizationId: string
): Promise<{ canRemove: boolean; reason?: string }> {
  try {
    const { db, workouts, workoutParticipants, members } = await import('@workspace/auth/server');
    const { eq, and } = await import('drizzle-orm');

    // Check if user is a customer
    if (!(await isCustomer(userId))) {
      return { canRemove: false, reason: 'Only customers can use self-removal' };
    }

    // Get user's member ID
    const memberId = await getUserMemberId(userId, organizationId);
    if (!memberId) {
      return { canRemove: false, reason: 'User not found in organization' };
    }

    // Check if the participant belongs to this user
    const [participant] = await db
      .select({
        customerId: workoutParticipants.customerId,
        workoutId: workoutParticipants.workoutId,
      })
      .from(workoutParticipants)
      .where(eq(workoutParticipants.id, participantId));

    if (!participant || participant.customerId !== memberId) {
      return { canRemove: false, reason: 'You can only remove yourself from workouts' };
    }

    // Get workout details to check timing
    const [workout] = await db
      .select({ startTime: workouts.startTime })
      .from(workouts)
      .where(and(eq(workouts.id, workoutId), eq(workouts.organizationId, organizationId)));

    if (!workout) {
      return { canRemove: false, reason: 'Workout not found' };
    }

    // Check 24-hour rule
    const now = new Date();
    const hoursUntilWorkout = (workout.startTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntilWorkout <= 24) {
      return {
        canRemove: false,
        reason: 'Cannot remove yourself from workouts within 24 hours of the scheduled time'
      };
    }

    return { canRemove: true };
  } catch (error) {
    console.error('❌ Error checking customer removal permission:', error);
    return { canRemove: false, reason: 'Permission check failed' };
  }
}

/**
 * Check if user can edit their own attendance status
 */
export async function canCustomerEditAttendance(
  userId: string,
  participantId: string,
  organizationId: string
): Promise<{ canEdit: boolean; reason?: string }> {
  try {
    const { db, workoutParticipants } = await import('@workspace/auth/server');
    const { eq } = await import('drizzle-orm');

    // Check if user is a customer
    if (!(await isCustomer(userId))) {
      return { canEdit: false, reason: 'Only customers can edit their own attendance' };
    }

    // Get user's member ID
    const memberId = await getUserMemberId(userId, organizationId);
    if (!memberId) {
      return { canEdit: false, reason: 'User not found in organization' };
    }

    // Check if the participant belongs to this user
    const [participant] = await db
      .select({ customerId: workoutParticipants.customerId })
      .from(workoutParticipants)
      .where(eq(workoutParticipants.id, participantId));

    if (!participant || participant.customerId !== memberId) {
      return { canEdit: false, reason: 'You can only edit your own attendance status' };
    }

    return { canEdit: true };
  } catch (error) {
    console.error('❌ Error checking customer attendance edit permission:', error);
    return { canEdit: false, reason: 'Permission check failed' };
  }
}

// Legacy function aliases for backward compatibility
// These will be removed once all usages are updated

/**
 * @deprecated Use isAdmin instead
 */
export async function canAccessAdminCached(userId: string): Promise<boolean> {
  return await isAdmin(userId);
}

/**
 * @deprecated Use canAccessOrganization instead
 */
export async function canAccessOrganizationCached(userId: string): Promise<boolean> {
  return await canAccessOrganization(userId);
}

/**
 * @deprecated Use canManageCustomers instead
 */
export async function canAccessCustomerCached(userId: string): Promise<boolean> {
  return await canManageCustomers(userId);
}

/**
 * @deprecated Use specific permission functions instead
 */
export async function checkPermissionCached(userId: string, permission: string): Promise<boolean> {
  switch (permission) {
    case 'admin':
      return await isAdmin(userId);
    case 'financial':
      return await canAccessFinancial(userId);
    case 'package':
      return await canReadPackages(userId);
    case 'organization':
      return await canAccessOrganization(userId);
    case 'customer':
      return await canManageCustomers(userId);
    default:
      return false;
  }
}

/**
 * @deprecated Use getCachedUserRole instead
 * Legacy function for backward compatibility
 */
export async function getAllCommonPermissionsCached(userId: string): Promise<Record<string, boolean>> {
  const role = await getCachedUserRole(userId);

  // Convert role to legacy permission format
  const permissions: Record<string, boolean> = {
    admin: role === 'admin',
    trainer: role === 'admin' || role === 'trainer',
    customer: role !== null,
    financial: role === 'admin',
    package: role === 'admin' || role === 'trainer',
    organization: role === 'admin' || role === 'trainer',
  };

  return permissions;
}
