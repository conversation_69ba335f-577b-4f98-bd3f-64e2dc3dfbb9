import { WorkoutResponse } from '@/lib/validations';
import { WORKOUT_STATUS_CONFIG } from '@/lib/workout-status-config';

export type CalendarView = 'month' | 'week' | 'day' | 'agenda';

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  color?: EventColor;
  label?: string;
  location?: string;
  // Workout-specific fields
  workoutId?: string;
  organizationId?: string;
  minParticipants?: number;
  maxParticipants?: number;
  participantCount?: number;
  status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  participants?: WorkoutParticipant[];
}

export interface WorkoutParticipant {
  id: string;
  customerId: string;
  customerName: string;
  status: 'enrolled' | 'confirmed' | 'cancelled';
  enrolledAt: Date;
  confirmedAt?: Date | null;
  creditDeducted: boolean;
}

export type EventColor = 'blue' | 'orange' | 'violet' | 'rose' | 'emerald' | 'indigo';

// Utility function to convert WorkoutResponse to CalendarEvent
export function workoutToCalendarEvent(workout: WorkoutResponse): CalendarEvent {
  const statusInfo =
    WORKOUT_STATUS_CONFIG[workout.status as keyof typeof WORKOUT_STATUS_CONFIG] || WORKOUT_STATUS_CONFIG.scheduled;

  return {
    id: workout.id,
    workoutId: workout.id,
    organizationId: workout.organizationId,
    title: workout.title,
    description: workout.description || undefined,
    start: new Date(workout.startTime),
    end: new Date(workout.endTime),
    location: workout.location || undefined,
    minParticipants: workout.minParticipants,
    maxParticipants: workout.maxParticipants,
    participantCount: workout.participantCount || workout.participants?.length || 0,
    status: workout.status as CalendarEvent['status'],
    participants:
      workout.participants?.map((p: NonNullable<WorkoutResponse['participants']>[number]) => ({
        id: p.id,
        customerId: p.customerId,
        customerName: p.customerName,
        status: p.status as WorkoutParticipant['status'],
        enrolledAt: new Date(p.enrolledAt),
        confirmedAt: p.confirmedAt ? new Date(p.confirmedAt) : null,
        creditDeducted: p.creditDeducted,
      })) || [],
    // Set color based on workout status
    color: statusInfo.color,
  };
}
