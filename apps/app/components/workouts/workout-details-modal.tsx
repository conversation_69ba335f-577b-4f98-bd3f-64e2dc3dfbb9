'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  RiCalendarLine,
  RiTimeLine,
  RiMapPinLine,
  RiUserLine,
  RiDeleteBinLine,
  RiEditLine,
  RiCheckLine,
  RiCloseLine,
  RiAddLine,
} from '@remixicon/react';
import {
  useWorkout,
  removeParticipant,
  updateParticipant,
  addParticipant,
  useWorkoutsList,
  updateWorkout,
} from '@/hooks/use-workouts';
import { useCustomersList } from '@/hooks/use-customers';
import { hasCustomerConflict, formatConflictMessage } from '@/lib/workout-utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { WorkoutResponse } from '@/lib/validations';
import { useAppPermission } from '@/components/permission-provider';

interface WorkoutDetailsModalProps {
  workoutId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (workout: WorkoutResponse) => void;
  onDelete?: (workoutId: string) => void;
  onWorkoutUpdated?: () => void;
  refreshTrigger?: number;
}

export function WorkoutDetailsModal({
  workoutId,
  open,
  onOpenChange,
  onEdit,
  onDelete,
  onWorkoutUpdated,
  refreshTrigger,
}: WorkoutDetailsModalProps) {
  const { workout, loading, error, mutate } = useWorkout(workoutId || '');
  const { customers } = useCustomersList({ limit: 100 });
  const { workouts: allWorkouts } = useWorkoutsList({ limit: 100 }); // Get all workouts for conflict checking
  const permissions = useAppPermission();

  // Refresh workout data when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      mutate();
    }
  }, [refreshTrigger, mutate]);
  const [removingParticipant, setRemovingParticipant] = useState<string | null>(null);
  const [updatingParticipant, setUpdatingParticipant] = useState<string | null>(null);
  const [addingParticipant, setAddingParticipant] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
  const [updatingStatus, setUpdatingStatus] = useState(false);

  if (!workoutId || !open) {
    return null;
  }

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl h-[80vh]">
          <div className="flex items-center justify-center py-8">
            <DialogTitle>
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading workout details...</p>
              </div>
            </DialogTitle>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error || !workout) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl h-[80vh]">
          <div className="flex items-center justify-center py-8">
            <DialogTitle>
              <div className="text-center">
                <p className="text-red-500 mb-2">Error loading workout</p>
                <p className="text-sm text-muted-foreground">{error || 'Workout not found'}</p>
              </div>
            </DialogTitle>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const handleRemoveParticipant = async (participantId: string) => {
    if (!workoutId) return;

    setRemovingParticipant(participantId);
    try {
      await removeParticipant(workoutId, participantId);
      mutate(); // Refresh workout data
    } catch (error) {
      console.error('Failed to remove participant:', error);
    } finally {
      setRemovingParticipant(null);
    }
  };

  const handleUpdateParticipantStatus = async (
    participantId: string,
    status: 'enrolled' | 'confirmed' | 'cancelled'
  ) => {
    if (!workoutId) return;

    setUpdatingParticipant(participantId);
    try {
      await updateParticipant(workoutId, participantId, { status });
      mutate(); // Refresh workout data
    } catch (error) {
      console.error('Failed to update participant status:', error);
    } finally {
      setUpdatingParticipant(null);
    }
  };

  const handleAddParticipant = async () => {
    if (!workoutId || !selectedCustomerId || !workout) return;

    // Check for conflicts
    const workoutStart = new Date(workout.startTime);
    const workoutEnd = new Date(workout.endTime);
    const { hasConflict, conflictingWorkout } = hasCustomerConflict(
      selectedCustomerId,
      workoutStart,
      workoutEnd,
      allWorkouts,
      workoutId // Exclude current workout
    );

    if (hasConflict && conflictingWorkout) {
      const customer = customers.find((c) => c.id === selectedCustomerId);
      const customerName = customer?.name || 'Customer';
      console.error(formatConflictMessage(customerName, conflictingWorkout));
      return;
    }

    setAddingParticipant(true);
    try {
      await addParticipant(workoutId, { customerId: selectedCustomerId });
      setSelectedCustomerId('');
      mutate(); // Refresh workout data
    } catch (error) {
      console.error('Failed to add participant:', error);
    } finally {
      setAddingParticipant(false);
    }
  };

  const handleStatusChange = async (newStatus: 'scheduled' | 'confirmed' | 'completed' | 'cancelled') => {
    if (!workoutId || !workout) return;

    // Validation rules for status changes
    if (newStatus === 'confirmed' && participantCount < workout.minParticipants) {
      console.error('Cannot confirm workout: minimum participants not met');
      return;
    }

    if (newStatus === 'completed' && workout.status !== 'confirmed') {
      console.error('Cannot complete workout: workout must be confirmed first');
      return;
    }

    setUpdatingStatus(true);
    try {
      await updateWorkout(workoutId, { status: newStatus });
      mutate(); // Refresh workout data
      onWorkoutUpdated?.(); // Refresh workout list in parent component
    } catch (error) {
      console.error('Failed to update workout status:', error);
    } finally {
      setUpdatingStatus(false);
    }
  };

  const participants = workout.participants || [];
  const participantCount = participants.length;
  const isAtMinimum = participantCount >= workout.minParticipants;
  const isAtCapacity = participantCount >= workout.maxParticipants;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-sm lg:max-w-4xl max-h-[90vh] overflow px-4 lg:px-6" showCloseButton={false}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{workout.title}</span>
            <Badge
              variant={workout.status as 'confirmed' | 'completed' | 'cancelled' | 'scheduled'}
              className="capitalize"
            >
              {workout.status}
            </Badge>
          </DialogTitle>
          <DialogDescription>Manage workout details and participants</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card className="gap-0 py-5">
            <CardHeader>
              <CardTitle className="text-lg">Workout Details</CardTitle>
            </CardHeader>
            <CardContent className="mt-2 space-y-2 flex-1">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <RiCalendarLine className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{format(new Date(workout.startTime), 'EEEE, MMMM d, yyyy')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <RiTimeLine className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {format(new Date(workout.startTime), 'h:mm a')} - {format(new Date(workout.endTime), 'h:mm a')}
                  </span>
                </div>
              </div>

              {workout.location && (
                <div className="flex items-center gap-2">
                  <RiMapPinLine className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{workout.location}</span>
                </div>
              )}

              {workout.description && (
                <div>
                  <p className="text-sm text-muted-foreground">{workout.description}</p>
                </div>
              )}

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <RiUserLine className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {participantCount}/{workout.maxParticipants} participants
                  </span>
                  {isAtMinimum && (
                    <Badge variant="secondary" className="text-xs">
                      Minimum met
                    </Badge>
                  )}
                  {isAtCapacity && (
                    <Badge variant="destructive" className="text-xs">
                      At capacity
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>

            {workout.status === 'completed' ? (
              <CardFooter className="text-sm text-muted-foreground">
                This workout has already been completed.
              </CardFooter>
            ) : permissions.hasWorkoutPermission ? (
              <CardFooter className="flex items-center gap-2 flex-wrap">
                <span className="text-sm text-muted-foreground">Change status:</span>

                {workout.status === 'scheduled' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange('confirmed')}
                    disabled={updatingStatus || participantCount < workout.minParticipants}
                    className="text-green-600 hover:text-green-700 text-xs"
                  >
                    {updatingStatus ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                    ) : (
                      <RiCheckLine className="h-3 w-3 mr-1" />
                    )}
                    Confirm
                  </Button>
                )}

                {workout.status === 'confirmed' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange('completed')}
                    disabled={updatingStatus}
                    className="text-emerald-600 hover:text-emerald-700 text-xs"
                  >
                    {updatingStatus ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                    ) : (
                      <RiCheckLine className="h-3 w-3 mr-1" />
                    )}
                    Complete
                  </Button>
                )}

                {(workout.status === 'scheduled' || workout.status === 'confirmed') && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStatusChange('cancelled')}
                    disabled={updatingStatus}
                    className="text-red-600 hover:text-red-700"
                  >
                    {updatingStatus ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                    ) : (
                      <RiCloseLine className="h-3 w-3 mr-1" />
                    )}
                    Cancel
                  </Button>
                )}

                {participantCount < workout.minParticipants && workout.status === 'scheduled' && (
                  <span className="text-xs text-muted-foreground">
                    Need {workout.minParticipants - participantCount} more participants to confirm
                  </span>
                )}
              </CardFooter>
            ) : (
              <CardFooter className="text-sm text-muted-foreground">
                Only trainers and admins can change workout status.
              </CardFooter>
            )}
          </Card>

          <Card className="gap-0 py-5">
            <CardHeader>
              <CardTitle className="text-lg">Participants ({participantCount})</CardTitle>
            </CardHeader>
            <CardContent className="mt-2">
              {/* Add Participant - Only trainers/admins can add participants */}
              {!isAtCapacity && permissions.hasWorkoutPermission && (
                <div className="mb-4 p-3 border rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2 mb-2">
                    <RiAddLine className="h-4 w-4" />
                    <span className="text-sm font-medium">Add Participant</span>
                  </div>
                  <div className="flex gap-2">
                    <Select value={selectedCustomerId} onValueChange={setSelectedCustomerId}>
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select a customer..." />
                      </SelectTrigger>
                      <SelectContent>
                        {customers
                          .filter((customer) => {
                            // Filter out customers already in the workout and those without sessions
                            const isAlreadyParticipant = participants.some((p) => p.customerId === customer.id);
                            return !isAlreadyParticipant && (customer.totalSessions || 0) > 0;
                          })
                          .map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name} ({customer.totalSessions || 0} sessions)
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={handleAddParticipant}
                      disabled={!selectedCustomerId || addingParticipant}
                      size="sm"
                    >
                      {addingParticipant ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b border-current"></div>
                      ) : (
                        <>
                          <RiAddLine className="h-4 w-4 mr-1" />
                          Add
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
              {participants.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <RiUserLine className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No participants enrolled yet</p>
                </div>
              ) : (
                <ScrollArea className="h-56 -mx-6 px-6">
                  <div className="space-y-2">
                    {participants.map((participant) => (
                      <div key={participant.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div>
                            <p className="font-medium">{participant.customerName}</p>
                            <p className="text-xs text-muted-foreground">
                              Enrolled {format(new Date(participant.enrolledAt), 'MMM d, h:mm a')}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge
                            variant={participant.status as 'enrolled' | 'confirmed' | 'cancelled'}
                            className="capitalize"
                          >
                            {participant.status}
                          </Badge>

                          {/* Status action buttons - trainers/admins can manage all, customers can only manage themselves */}
                          {participant.status === 'enrolled' && permissions.hasWorkoutPermission && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleUpdateParticipantStatus(participant.id, 'confirmed')}
                              disabled={updatingParticipant === participant.id}
                              className="h-7 px-2"
                            >
                              <RiCheckLine className="h-3 w-3" />
                            </Button>
                          )}

                          {/* Remove button - trainers/admins can remove anyone, customers see this for their own participation (API will enforce 24-hour rule) */}
                          {(permissions.hasWorkoutPermission || permissions.isCustomerUser) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRemoveParticipant(participant.id)}
                              disabled={removingParticipant === participant.id}
                              className="h-7 px-2 text-red-600 hover:text-red-700"
                              title={permissions.isCustomerUser && !permissions.hasWorkoutPermission ?
                                "Remove yourself from this workout (24-hour cancellation rule applies)" :
                                "Remove participant from workout"}
                            >
                              {removingParticipant === participant.id ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                              ) : (
                                <RiDeleteBinLine className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} className="mr-auto">
            Close
          </Button>

          {/* Only trainers/admins can edit workout details */}
          {onEdit && permissions.hasWorkoutPermission && (
            <Button variant="outline" onClick={() => onEdit(workout)}>
              <RiEditLine className="h-4 w-4 mr-2" />
              Edit Workout
            </Button>
          )}

          {/* Only trainers/admins can delete workouts */}
          {onDelete && permissions.hasWorkoutPermission && (
            <Button variant="destructive" onClick={() => onDelete(workout.id)}>
              <RiDeleteBinLine className="h-4 w-4 mr-2" />
              Delete Workout
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
