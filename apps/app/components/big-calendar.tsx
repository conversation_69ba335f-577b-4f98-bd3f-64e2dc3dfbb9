'use client';

import { useState, useMemo } from 'react';
import { startOfWeek, endOfWeek } from 'date-fns';
import { useCalendarContext } from '@/components/event-calendar/calendar-context';
import { useWorkoutHandlers } from '@/components/calendar-page-wrapper';
import { useWorkoutsList, createWorkout, deleteWorkout } from '@/hooks/use-workouts';

import { WorkoutDetailsModal } from '@/components/workouts/workout-details-modal';
import { WorkoutEditModal } from '@/components/workouts/workout-edit-modal';

import {
  EventCalendar,
  type CalendarEvent,
  // type EventColor,
  workoutToCalendarEvent,
  EventGap,
  EventHeight,
  WeekCellsHeight,
} from '@/components/event-calendar';

import { hasWorkoutTimeConflict } from '@/lib/workout-utils';
import { toast } from 'sonner';
import { SidebarTrigger, useSidebar } from './ui/sidebar';
import { cn } from '@/lib/utils';
import { filterableStatuses } from '@/lib/workout-status-config';
import { Workout } from '@workspace/auth';
import { useAppPermission } from './permission-provider';

// Etiquettes data for calendar filtering
export const etiquettes = filterableStatuses.map((status) => ({
  id: status.key,
  name: status.label,
  color: status.color,
  isActive: true, // Default to active
}));

export default function Component() {
  const { currentDate, isColorVisible } = useCalendarContext();
  const { handleWorkoutUpdate: contextWorkoutUpdate, mutate: contextMutate } = useWorkoutHandlers();
  const [selectedWorkoutId, setSelectedWorkoutId] = useState<string | null>(null);
  const [workoutModalOpen, setWorkoutModalOpen] = useState(false);
  const [editWorkout, setEditWorkout] = useState<Workout | null>(null);
  const [workoutEditModalOpen, setWorkoutEditModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Calculate date range for fetching workouts (current week)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 0 });

  // Fetch workouts for the current date range
  const { workouts, error } = useWorkoutsList({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    limit: 100, // Maximum allowed by API
  });

  // Convert workouts to calendar events
  const events = useMemo(() => {
    return workouts.map(workoutToCalendarEvent);
  }, [workouts]);

  // Filter events based on visible colors
  const visibleEvents = useMemo(() => {
    return events.filter((event) => isColorVisible(event.color));
  }, [events, isColorVisible]);

  const { leftSidebarOpen, rightSidebarOpen } = useSidebar();

  const { hasWorkoutPermission } = useAppPermission();

  const handleWorkoutDeleteFromCalendar = async (eventId: string) => {
    try {
      // Find the workout ID from the event
      const event = events.find((e) => e.id === eventId);
      if (!event?.workoutId) {
        console.error('Cannot delete workout without workoutId');
        return;
      }

      await deleteWorkout(event.workoutId);
      toast.success('Workout deleted successfully');
      // Refresh the workout list
      contextMutate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete workout';
      toast.error(errorMessage);
    }
  };

  const handleWorkoutClick = (event: CalendarEvent) => {
    if (event.workoutId) {
      setSelectedWorkoutId(event.workoutId);
      setWorkoutModalOpen(true);
    }
  };

  const handleWorkoutEdit = (workout: Workout) => {
    setEditWorkout(workout);
    setWorkoutEditModalOpen(true);
    setWorkoutModalOpen(false);
  };

  const handleWorkoutDelete = async (workoutId: string) => {
    try {
      await deleteWorkout(workoutId);
      toast.success('Workout deleted successfully');
      setWorkoutModalOpen(false);
      contextMutate(); // Refresh the workout list
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete workout';
      toast.error(errorMessage);
    }
  };

  const handleWorkoutCreate = async (startTime: Date) => {
    try {
      // Create new workout at the clicked time
      const workoutStart = new Date(startTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for workout time conflicts before creating new workout
      const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
        workoutStart,
        workoutEnd,
        workouts
      );

      if (hasTimeConflict && conflictingWorkouts.length > 0) {
        toast.error('Cannot create workout: time slot conflicts with existing workout');
        return;
      }

      // Create new workout
      const newWorkout = await createWorkout({
        title: `Training Session`,
        description: '',
        startTime: workoutStart.toISOString(),
        endTime: workoutEnd.toISOString(),
        location: '',
        minParticipants: 1,
        maxParticipants: 5,
      });

      if (newWorkout) {
        toast.success('Workout created successfully');
        // Refresh the workout list
        contextMutate();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create workout';
      toast.error(errorMessage);
    }
  };

  if (error) {
    return (
      <div className="flex-1 min-w-0">
        <div
          className="flex has-data-[slot=month-view]:flex-1 flex-col rounded-lg"
          style={
            {
              '--event-height': `${EventHeight}px`,
              '--event-gap': `${EventGap}px`,
              '--week-cells-height': `${WeekCellsHeight}px`,
            } as React.CSSProperties
          }
        >
          <div className={cn('flex flex-col sm:flex-row sm:items-center justify-between gap-2 py-5 sm:px-4')}>
            <div className="flex max-sm:items-center justify-between gap-1.5">
              <SidebarTrigger
                data-state={leftSidebarOpen ? 'invisible' : 'visible'}
                className="peer size-7 text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent! sm:-ms-1.5 lg:data-[state=invisible]:opacity-0 lg:data-[state=invisible]:pointer-events-none transition-opacity ease-in-out duration-200"
                isOutsideSidebar
              />

              <SidebarTrigger
                data-state={rightSidebarOpen ? 'invisible' : 'visible'}
                className="peer size-7 text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent! sm:-ms-1.5 lg:data-[state=invisible]:opacity-0 lg:data-[state=invisible]:pointer-events-none transition-opacity ease-in-out duration-200"
                isOutsideSidebar
                direction="right"
              />
            </div>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-500 mb-2">Error loading workouts</p>
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex-1 min-w-0">
        <EventCalendar
          events={visibleEvents}
          onEventCreate={handleWorkoutCreate}
          onEventUpdate={contextWorkoutUpdate}
          onEventDelete={handleWorkoutDeleteFromCalendar}
          onEventClick={handleWorkoutClick}
          initialView="week"
        />
      </div>

      {hasWorkoutPermission && (
        <WorkoutDetailsModal
          workoutId={selectedWorkoutId}
          open={workoutModalOpen}
          onOpenChange={setWorkoutModalOpen}
          onEdit={handleWorkoutEdit}
          onDelete={handleWorkoutDelete}
          onWorkoutUpdated={() => contextMutate()} // Pass callback to refresh workout list when workout is updated
          refreshTrigger={refreshTrigger}
        />
      )}

      {hasWorkoutPermission && (
        <WorkoutEditModal
          workout={editWorkout}
          open={workoutEditModalOpen}
          onOpenChange={setWorkoutEditModalOpen}
          onSuccess={() => {
            contextMutate(); // Refresh workout list
            setEditWorkout(null);
            setRefreshTrigger((prev) => prev + 1); // Trigger refresh of workout details modal
          }}
        />
      )}
    </>
  );
}
