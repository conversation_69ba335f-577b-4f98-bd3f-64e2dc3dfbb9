'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { RiExpandUpDownLine, RiUserLine, RiLogoutCircleLine, RiLoader4Line } from '@remixicon/react';
import { useSession, signOut } from '@workspace/auth/client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { notify } from '@/lib/notification-service';
import Link from 'next/link';
import { Button } from './ui/button';
import { useAppPermission } from './permission-provider';

export function NavUser() {
  const { data: session, isPending } = useSession();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const router = useRouter();

  const user = session?.user;

  const appPermission = useAppPermission();

  // Generate initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut({
        fetchOptions: {
          onSuccess: () => {
            notify.show({
              type: 'success',
              title: 'Signed Out',
              message: 'You have been signed out successfully',
            });
            router.push('/auth/sign-in');
          },
          onError: (ctx: any) => {
            console.error('Sign out error:', ctx.error);
            notify.show({
              type: 'error',
              title: 'Sign Out Failed',
              message: 'Failed to sign out. Please try again.',
            });
          },
        },
      });
    } catch (error) {
      console.error('Sign out error:', error);
      notify.show({
        type: 'error',
        title: 'Sign Out Failed',
        message: 'Failed to sign out. Please try again.',
      });
    } finally {
      setIsSigningOut(false);
    }
  };

  if (isPending) {
    // Show loading state while session is being fetched
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" disabled>
            <div className="size-8 rounded-lg bg-muted animate-pulse" />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <div className="h-4 bg-muted rounded animate-pulse" />
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  if (isPending || !user) {
    // Not logged in
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <Button asChild className="w-full">
            <Link href="/auth/sign-in">Sign In</Link>
          </Button>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  const description = appPermission.hasCustomerPermission
    ? user.email // admin/trainer
    : user.username; // customer

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground [&>svg]:size-5"
            >
              <Avatar className="size-8">
                <AvatarImage src={user.image || ''} alt={user.name} />
                <AvatarFallback className="rounded-lg">{getInitials(user.name)}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{user.name}</span>
                <span className="truncate text-xs text-muted-foreground">{description}</span>
              </div>
              <RiExpandUpDownLine className="ml-auto size-5 text-muted-foreground/80" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side="bottom"
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="size-8">
                  <AvatarImage src={user.image || ''} alt={user.name} />
                  <AvatarFallback className="rounded-lg">{getInitials(user.name)}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{user.name}</span>
                  <span className="truncate text-xs text-muted-foreground">{description}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                className="gap-3 focus:bg-sidebar-accent cursor-pointer"
                onClick={() => {
                  // TODO: Implement profile page navigation
                  notify.show({
                    type: 'info',
                    title: 'Coming Soon',
                    message: 'Profile page is coming soon',
                  });
                }}
              >
                <RiUserLine size={16} className="text-muted-foreground" />
                Profile
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="gap-3 focus:bg-sidebar-accent cursor-pointer text-red-600 focus:text-red-600"
              onClick={handleSignOut}
              disabled={isSigningOut}
            >
              {isSigningOut ? <RiLoader4Line size={16} className="animate-spin" /> : <RiLogoutCircleLine size={16} />}
              {isSigningOut ? 'Signing out...' : 'Sign out'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
