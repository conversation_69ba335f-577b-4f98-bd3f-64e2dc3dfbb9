'use client';

import { useDraggable } from '@dnd-kit/core';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { RiUserLine, RiCoinLine } from '@remixicon/react';
import type { CustomerResponse } from '@/lib/validations';

interface DraggableCustomerItemProps {
  customer: CustomerResponse;
  isDragging?: boolean;
  canDrag?: boolean;
}

export function DraggableCustomerItem({ customer, isDragging = false, canDrag = true }: DraggableCustomerItemProps) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: `customer-${customer.id}`,
    data: {
      type: 'customer',
      customer,
    },
  });

  // Don't apply transform to keep the original card in place
  // The drag overlay will handle the visual feedback
  const style = {};

  const hasSessions = (customer.totalSessions || 0) > 0;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`
        ${canDrag ? 'cursor-grab active:cursor-grabbing' : 'cursor-not-allowed'}
        transition-all duration-200
        ${isDragging ? 'opacity-50 scale-95' : 'hover:shadow-md'}
        ${!hasSessions ? 'opacity-60 border-muted' : 'border-border'}
      `}
      {...(canDrag ? listeners : {})}
      {...(canDrag ? attributes : {})}
    >
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <div className="flex-shrink-0">
              <RiUserLine className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="font-medium text-sm truncate">{customer.name}</p>
              {customer.parentName && <p className="text-xs text-muted-foreground truncate">{customer.parentName}</p>}
            </div>
          </div>

          <div className="flex items-center gap-1 flex-shrink-0">
            <RiCoinLine className="h-3 w-3 text-muted-foreground" />
            <Badge variant={hasSessions ? 'default' : 'secondary'} className="text-xs">
              {customer.totalSessions || 0}
            </Badge>
          </div>
        </div>

        {!hasSessions && <div className="mt-2 text-xs text-muted-foreground">No sessions available</div>}
      </CardContent>
    </Card>
  );
}
