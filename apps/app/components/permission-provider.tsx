'use client';

import { createContext, useContext } from 'react';

export type AppPermission = {
  hasFinancialPermission: boolean;
  hasPackagePermission: boolean;
  hasOrganizationPermission: boolean;
  hasCustomerPermission: boolean;
  hasWorkoutPermission: boolean;
};

const AppPermissionContext = createContext<AppPermission | null>(null);

export function useAppPermission() {
  const ctx = useContext(AppPermissionContext);
  if (!ctx) {
    throw new Error('useAppPermission must be used within AppPermissionProvider');
  }

  return ctx;
}

export function AppPermissionProvider({ value, children }: { value: AppPermission; children: React.ReactNode }) {
  return <AppPermissionContext.Provider value={value}>{children}</AppPermissionContext.Provider>;
}
