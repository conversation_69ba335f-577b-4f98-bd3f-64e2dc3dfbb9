'use client';

import useSWR, { mutate as globalMutate } from 'swr';
import type {
  WorkoutResponse,
  CreateWorkoutInput,
  UpdateWorkoutInput,
  WorkoutQueryInput,
  WorkoutParticipantResponse,
  AddParticipantInput,
  UpdateParticipantInput,
  PaginatedResponse,
} from '@/lib/validations';
import { publicFetcher } from '@/lib/fetcher';

export function getWorkoutsKey(params?: Partial<WorkoutQueryInput>) {
  const searchParams = new URLSearchParams();
  if (params?.search) searchParams.set('search', params.search);
  if (params?.status) searchParams.set('status', params.status);
  if (params?.startDate) searchParams.set('startDate', params.startDate);
  if (params?.endDate) searchParams.set('endDate', params.endDate);
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.offset) searchParams.set('offset', params.offset.toString());
  if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
  return `/api/workouts?${searchParams.toString()}`;
}

export function getWorkoutKey(id: string) {
  return `/api/workouts/${id}`;
}

export function getWorkoutParticipantsKey(workoutId: string) {
  return `/api/workouts/${workoutId}/participants`;
}

export function useWorkoutsList(params?: Partial<WorkoutQueryInput>) {
  const key = getWorkoutsKey(params);
  const { data, error, isLoading, mutate } = useSWR<PaginatedResponse<WorkoutResponse>>(key, publicFetcher);

  return {
    workouts: data?.data || [],
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    pagination: data?.pagination || { total: 0, limit: 50, offset: 0, hasMore: false },
    mutate,
  };
}

export function useWorkout(id: string) {
  const key = getWorkoutKey(id);
  const { data, error, isLoading, mutate } = useSWR<WorkoutResponse>(key, publicFetcher);

  return {
    workout: data,
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    mutate,
  };
}

export function useWorkoutParticipants(workoutId: string) {
  const key = getWorkoutParticipantsKey(workoutId);
  const { data, error, isLoading, mutate } = useSWR<WorkoutParticipantResponse[]>(key, publicFetcher);

  return {
    participants: data || [],
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    mutate,
  };
}

export async function createWorkout(workoutData: CreateWorkoutInput, params?: Partial<WorkoutQueryInput>) {
  const response = await fetch('/api/workouts', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(workoutData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to create workout');
  }
  const newWorkout: WorkoutResponse = await response.json();
  await globalMutate(getWorkoutsKey(params));

  return newWorkout;
}

export async function updateWorkout(id: string, workoutData: UpdateWorkoutInput, params?: Partial<WorkoutQueryInput>) {
  const response = await fetch(`/api/workouts/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(workoutData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update workout');
  }
  const updatedWorkout: WorkoutResponse = await response.json();
  await globalMutate(getWorkoutsKey(params));
  await globalMutate(getWorkoutKey(id));

  return updatedWorkout;
}

export async function deleteWorkout(id: string, params?: Partial<WorkoutQueryInput>) {
  const response = await fetch(`/api/workouts/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to delete workout');
  }
  await globalMutate(getWorkoutsKey(params));
}

export async function addParticipant(workoutId: string, participantData: AddParticipantInput) {
  const response = await fetch(`/api/workouts/${workoutId}/participants`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(participantData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to add participant');
  }
  const newParticipant: WorkoutParticipantResponse = await response.json();
  await globalMutate(getWorkoutKey(workoutId));
  await globalMutate(getWorkoutParticipantsKey(workoutId));

  return newParticipant;
}

export async function updateParticipant(
  workoutId: string,
  participantId: string,
  participantData: UpdateParticipantInput
) {
  const response = await fetch(`/api/workouts/${workoutId}/participants/${participantId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(participantData),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to update participant');
  }
  const updatedParticipant: WorkoutParticipantResponse = await response.json();
  await globalMutate(getWorkoutKey(workoutId));
  await globalMutate(getWorkoutParticipantsKey(workoutId));

  return updatedParticipant;
}

export async function removeParticipant(workoutId: string, participantId: string) {
  const response = await fetch(`/api/workouts/${workoutId}/participants/${participantId}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to remove participant');
  }
  await globalMutate(getWorkoutKey(workoutId));
  await globalMutate(getWorkoutParticipantsKey(workoutId));
}
