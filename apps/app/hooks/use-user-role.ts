'use client';

import { useState, useEffect } from 'react';
import { useSession } from '@workspace/auth';

export type UserRole = 'admin' | 'trainer' | 'customer';

interface UseUserRoleReturn {
  role: UserRole | null;
  isLoading: boolean;
  isAdmin: boolean;
  isTrainerOrAdmin: boolean;
  canAccessFinancial: boolean;
  canManagePackages: boolean;
  canReadPackages: boolean;
  canAccessOrganization: boolean;
  canManageCustomers: boolean;
  canManageWorkouts: boolean;
}

/**
 * Client-side hook to get user role and permissions
 * This fetches the role from the server and provides convenient permission checks
 */
export function useUserRole(): UseUserRoleReturn {
  const { data: session } = useSession();
  const [role, setRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRole = async () => {
      if (!session?.user?.id) {
        setRole(null);
        setIsLoading(false);
        return;
      }

      try {
        // Call our API to get the user's role
        const response = await fetch('/api/user/role');
        if (response.ok) {
          const data = await response.json();
          setRole(data.role);
        } else {
          console.error('Failed to fetch user role');
          setRole(null);
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
        setRole(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRole();
  }, [session?.user?.id]);

  // Permission calculations based on role
  const isAdmin = role === 'admin';
  const isTrainerOrAdmin = role === 'admin' || role === 'trainer';

  return {
    role,
    isLoading,
    isAdmin,
    isTrainerOrAdmin,
    canAccessFinancial: isAdmin,
    canManagePackages: isAdmin,
    canReadPackages: isTrainerOrAdmin,
    canAccessOrganization: isTrainerOrAdmin,
    canManageCustomers: isTrainerOrAdmin,
    canManageWorkouts: isTrainerOrAdmin,
  };
}
