import useSWR from 'swr';
import type { CreatePackageInput, UpdatePackageInput, PackageResponse, PaginatedResponse } from '@/lib/validations';
import { fetcher } from '@/lib/fetcher';

const API_BASE = '/api/packages';

// Key generator for packages list
export const getPackagesKey = (pageIndex: number, limit: number = 20, search?: string, isActive?: boolean) => {
  const params = new URLSearchParams({
    limit: limit.toString(),
    offset: (pageIndex * limit).toString(),
  });

  if (search) {
    params.append('search', search);
  }

  if (isActive !== undefined) {
    params.append('isActive', isActive.toString());
  }

  return `${API_BASE}?${params.toString()}`;
};

// Hook to get packages list
export function usePackages(search?: string, isActive?: boolean) {
  const key = getPackagesKey(0, 20, search, isActive);

  return useSWR<PaginatedResponse<PackageResponse>>(key, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 5000,
  });
}

// Hook to get a specific package
export function usePackage(packageId: string) {
  const key = packageId ? `${API_BASE}/${packageId}` : null;

  return useSWR<PackageResponse>(key, fetcher, {
    revalidateOnFocus: false,
  });
}

// Create a new package
export async function createPackage(packageData: CreatePackageInput): Promise<PackageResponse> {
  const response = await fetch(API_BASE, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(packageData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create package');
  }

  return response.json();
}

// Update an existing package
export async function updatePackage(packageId: string, packageData: UpdatePackageInput): Promise<PackageResponse> {
  const response = await fetch(`${API_BASE}/${packageId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(packageData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update package');
  }

  return response.json();
}

// Delete (deactivate) a package
export async function deletePackage(packageId: string): Promise<PackageResponse> {
  const response = await fetch(`${API_BASE}/${packageId}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to delete package');
  }

  return response.json();
}

// Get active packages for selection (useful for dropdowns)
export function useActivePackages() {
  return usePackages(undefined, true);
}
