import useSWR from 'swr';
import type { AssignPackageInput, CustomerPackageResponse, PaginatedResponse } from '@/lib/validations';
import { fetcher } from '@/lib/fetcher';

// Hook to get customer packages
export function useCustomerPackages(customerId: string, includeExpired: boolean = false) {
  const params = new URLSearchParams();
  if (includeExpired) {
    params.append('includeExpired', 'true');
  }

  const key = customerId ? `/api/customers/${customerId}/packages?${params.toString()}` : null;

  return useSWR<PaginatedResponse<CustomerPackageResponse>>(key, fetcher, {
    revalidateOnFocus: false,
    dedupingInterval: 5000,
  });
}

// Assign a package to a customer
export async function assignPackageToCustomer(
  customerId: string,
  packageData: AssignPackageInput
): Promise<CustomerPackageResponse> {
  const response = await fetch(`/api/customers/${customerId}/packages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(packageData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to assign package');
  }

  return response.json();
}
