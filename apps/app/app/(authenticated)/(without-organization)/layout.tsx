import { ReactNode } from 'react';
// import { cookies } from 'next/headers';
// import { redirect } from 'next/navigation';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  // const cookieHeader = (await cookies()).toString();

  // // Check onboarding status server-side
  // const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`, {
  //   headers: { Cookie: cookieHeader },
  //   cache: 'no-store',
  // });

  // console.log('⚠️ Onboarding: ', res.status);

  // if (res.status !== 404) {
  //   // Onboarded, redirect to home
  //   console.log('✅ Already onboarded');
  //   redirect('/');
  // }

  // console.log('🐝 Proceed with onboarding');
  return <>{children}</>;
}
