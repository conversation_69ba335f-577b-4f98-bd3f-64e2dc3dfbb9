'use client';

import { useAppPermission } from '@/components/permission-provider';
import { CustomerManagement } from '@/components/customers/customer-management';
import { RedirectToSignIn } from '@workspace/auth';

export default function CustomersView() {
  const { hasCustomerPermission } = useAppPermission();

  if (!hasCustomerPermission) {
    return <RedirectToSignIn />;
  }

  return <CustomerManagement />;
}
