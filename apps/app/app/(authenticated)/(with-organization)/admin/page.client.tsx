'use client';

import { <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { SessionManagementDashboard } from '@/components/admin/session-management-dashboard';
import { MemberManagement } from '@/components/admin/member-management';
import { PackageManagement } from '@/components/admin/package-management';
import { useAppPermission } from '@/components/permission-provider';

export default function AdminView() {
  const { hasFinancialPermission, hasPackagePermission, hasOrganizationPermission } = useAppPermission();

  return (
    <>
      <TabsList className="grid w-full grid-cols-3">
        {hasFinancialPermission && <TabsTrigger value="sessions">Session Management</TabsTrigger>}
        {hasPackagePermission && <TabsTrigger value="packages">Package Management</TabsTrigger>}
        {hasOrganizationPermission && <TabsTrigger value="members">Members Management</TabsTrigger>}
      </TabsList>
      {hasFinancialPermission && (
        <TabsContent value="sessions" className="mt-6">
          <SessionManagementDashboard />
        </TabsContent>
      )}
      {hasPackagePermission && (
        <TabsContent value="packages" className="mt-6">
          <PackageManagement />
        </TabsContent>
      )}
      {hasOrganizationPermission && (
        <TabsContent value="members" className="mt-6">
          <MemberManagement />
        </TabsContent>
      )}
    </>
  );
}
