import { ReactNode } from 'react';
import {
  // cookies,
  headers,
} from 'next/headers';
// import { redirect } from 'next/navigation';
import { CalendarProvider } from '@/components/event-calendar/calendar-context';
import { RedirectToSignIn } from '@workspace/auth';
import {
  canAccessFinancial,
  canReadPackages,
  canAccessOrganization,
  canManageCustomers,
  canManageWorkouts,
} from '@/lib/permission-utils';
// import { auth } from '@workspace/auth/server';
import { AppPermissionProvider } from '@/components/permission-provider';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { getAuthenticatedUser } from '@/lib/api-utils';
import { AppSidebar } from '@/components/app-sidebar';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  // const cookieStore = await cookies();

  // TODO: what is the cookie doing
  // Check onboarding status server-side
  // const trainerResponse = await fetch(
  //   `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`,
  //   {
  //     headers: { Cookie: cookieStore.toString() },
  //     cache: 'no-store',
  //   }
  // );

  // if (trainerResponse.status === 404) {
  //   // Not onboarded, redirect to onboarding
  //   redirect('/onboarding');
  // }

  // Get user session for permission checking
  let hasFinancialPermission = false;
  let hasPackagePermission = false;
  let hasOrganizationPermission = false;
  let hasCustomerPermission = false;
  let hasWorkoutPermission = false;

  const userSession = await getAuthenticatedUser(undefined, await headers());
  if (!userSession) {
    console.debug('❌ No user session');
    return <RedirectToSignIn />;
  }

  // console.debug('❓ userSession', userSession?.session);
  // if (!userSession?.session?.activeOrganizationId) {
  //   console.debug('❌ No active organization');

  //   // Still using old sessions before onboarding, need to refresh
  //   await auth.api.setActiveOrganization({
  //     body: { organizationId: (await trainerResponse.json()).organizationId },
  //   });

  //   redirect('/');
  // }

  // // Check role-based permissions
  hasFinancialPermission = await canAccessFinancial(userSession.user.id);
  hasPackagePermission = await canReadPackages(userSession.user.id);
  hasOrganizationPermission = await canAccessOrganization(userSession.user.id);
  hasCustomerPermission = await canManageCustomers(userSession.user.id);
  hasWorkoutPermission = await canManageWorkouts(userSession.user.id);

  return (
    <AppPermissionProvider
      value={{
        hasFinancialPermission,
        hasPackagePermission,
        hasOrganizationPermission,
        hasCustomerPermission,
        hasWorkoutPermission,
      }}
    >
      <CalendarProvider>
        <SidebarProvider>
          <AppSidebar />
          <SidebarInset>
            <div className="flex flex-1 flex-col gap-4 p-2 pt-0">{children}</div>
          </SidebarInset>
          <CustomerSidebar />
        </SidebarProvider>
      </CalendarProvider>
    </AppPermissionProvider>
  );
}
