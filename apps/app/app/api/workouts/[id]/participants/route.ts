import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, members, users, organizations } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { addParticipantSchema } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-utils';
import { validateParticipantBusinessRules } from '@/lib/business-rules-service';

// GET /api/workouts/[id]/participants - Get workout participants
export const GET = withAuth(
  async (_request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Verify workout exists and belongs to organization
      const [workout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.organizationId, organizationId)));

      if (!workout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Get participants
      const participants = await db
        .select({
          id: workoutParticipants.id,
          workoutId: workoutParticipants.workoutId,
          customerId: workoutParticipants.customerId,
          customerName: users.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .innerJoin(users, eq(members.userId, users.id))
        .innerJoin(organizations, eq(members.organizationId, organizations.id))
        .where(and(eq(workoutParticipants.workoutId, id), eq(organizations.id, organizationId)));

      return createSuccessResponse(participants);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// POST /api/workouts/[id]/participants - Add participant to workout
export const POST = withAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      const bodyValidation = await validateRequestBody(request, addParticipantSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const { customerId } = bodyValidation.data;

      // Validate business rules
      const validation = await validateParticipantBusinessRules({
        organizationId,
        workoutId: id,
        customerId,
      });

      if (!validation.valid) {
        return createErrorResponse('Bad Request', validation.message, 400);
      }

      // Add participant
      const [newParticipant] = await db
        .insert(workoutParticipants)
        .values({
          workoutId: id,
          customerId,
          status: 'enrolled',
        })
        .returning();

      if (!newParticipant) {
        throw new Error('Failed to add participant');
      }

      // Get participant with customer name for response
      const [participantWithCustomer] = await db
        .select({
          id: workoutParticipants.id,
          workoutId: workoutParticipants.workoutId,
          customerId: workoutParticipants.customerId,
          customerName: users.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .innerJoin(users, eq(members.userId, users.id))
        .innerJoin(organizations, eq(members.organizationId, organizations.id))
        .where(and(eq(workoutParticipants.id, newParticipant.id), eq(organizations.id, organizationId)));

      return createSuccessResponse(participantWithCustomer, 201);
    } catch (error) {
      return handleApiError(error);
    }
  }
);
