import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, members, users, organizations } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateParticipantSchema } from '@/lib/validations';
import {
  withAuth,
  withCustomerAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-utils';
import { transitionParticipantStatus } from '@/lib/session-status-service';
import { canCustomerEditAttendance, isTrainerOrAdmin } from '@/lib/permission-utils';
import { validateCustomerRemovalRules } from '@/lib/business-rules-service';

// PUT /api/workouts/[id]/participants/[participantId] - Update participant status
export const PUT = withCustomerAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string; participantId: string }> }) => {
    try {
      const { id, participantId } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      const bodyValidation = await validateRequestBody(request, updateParticipantSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const { status } = bodyValidation.data;

      // Check permissions: trainers/admins can edit anyone, customers can only edit themselves
      const isTrainerOrAdminUser = await isTrainerOrAdmin(userSession.user.id);

      if (!isTrainerOrAdminUser) {
        // Customer permission check
        const { canEdit, reason } = await canCustomerEditAttendance(
          userSession.user.id,
          participantId,
          organizationId
        );

        if (!canEdit) {
          return createErrorResponse('Forbidden', reason || 'Cannot edit this participant', 403);
        }
      }

      // Use status service to handle the transition with business rules
      const statusResult = await transitionParticipantStatus(id, participantId, status, organizationId);

      if (!statusResult.success) {
        return createErrorResponse('Bad Request', statusResult.message, 400);
      }

      // Get updated participant with customer name for response
      const [participantWithCustomer] = await db
        .select({
          id: workoutParticipants.id,
          workoutId: workoutParticipants.workoutId,
          customerId: workoutParticipants.customerId,
          customerName: users.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .innerJoin(users, eq(members.userId, users.id))
        .innerJoin(organizations, eq(members.organizationId, organizations.id))
        .where(and(eq(workoutParticipants.id, participantId), eq(organizations.id, organizationId)));

      return createSuccessResponse(participantWithCustomer);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// DELETE /api/workouts/[id]/participants/[participantId] - Remove participant from workout
export const DELETE = withCustomerAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string; participantId: string }> }) => {
    try {
      const { id, participantId } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Check permissions: trainers/admins can remove anyone, customers can only remove themselves with 24-hour rule
      const isTrainerOrAdminUser = await isTrainerOrAdmin(userSession.user.id);

      if (!isTrainerOrAdminUser) {
        // Customer self-removal validation
        const validation = await validateCustomerRemovalRules(
          id,
          participantId,
          userSession.user.id,
          organizationId
        );

        if (!validation.valid) {
          return createErrorResponse('Forbidden', validation.message, 403);
        }
      }

      // Verify workout exists and belongs to organization
      const [workout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.organizationId, organizationId)));

      if (!workout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Verify participant exists and belongs to this workout and organization
      const [participant] = await db
        .select()
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .where(
          and(
            eq(workoutParticipants.id, participantId),
            eq(workoutParticipants.workoutId, id),
            eq(members.organizationId, organizationId)
          )
        );

      if (!participant) {
        return createErrorResponse('Not Found', 'Participant not found', 404);
      }

      // Remove participant
      await db.delete(workoutParticipants).where(eq(workoutParticipants.id, participantId));

      return createSuccessResponse({ message: 'Participant removed successfully' });
    } catch (error) {
      return handleApiError(error);
    }
  }
);
