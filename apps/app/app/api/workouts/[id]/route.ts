import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, members, users, NewWorkout } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateWorkoutSchema, type WorkoutResponse } from '@/lib/validations';
import {
  withAuth,
  withTrainer<PERSON><PERSON>,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-utils';
import { transitionWorkoutStatus } from '@/lib/session-status-service';
import { validateWorkoutBusinessRules } from '@/lib/business-rules-service';

// GET /api/workouts/[id] - Get single workout with participants
export const GET = withAuth(
  async (_request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Get workout with participants
      const [workout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.organizationId, organizationId)));

      if (!workout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Get participants
      const participants = await db
        .select({
          id: workoutParticipants.id,
          customerId: workoutParticipants.customerId,
          customerName: users.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .innerJoin(users, eq(members.userId, users.id))
        .where(eq(workoutParticipants.workoutId, id));

      const response: WorkoutResponse = {
        ...workout,
        participants,
        participantCount: participants.length,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// PUT /api/workouts/[id] - Update workout (trainers/admins only)
export const PUT = withTrainerAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      const bodyValidation = await validateRequestBody(request, updateWorkoutSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const updateData = bodyValidation.data;

      // Check if workout exists and belongs to organization
      const [existingWorkout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.organizationId, organizationId)));

      if (!existingWorkout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Handle status change separately using status service
      if (updateData.status !== undefined) {
        const statusResult = await transitionWorkoutStatus(id, updateData.status, organizationId);
        if (!statusResult.success) {
          return createErrorResponse('Bad Request', statusResult.message, 400);
        }
      }

      // Validate business rules if time-related fields are being updated
      const isTimeUpdate = updateData.startTime !== undefined || updateData.endTime !== undefined;
      if (isTimeUpdate) {
        // Get all organization's workouts for conflict checking
        const allWorkouts = await db.select().from(workouts).where(eq(workouts.organizationId, organizationId));

        const validation = await validateWorkoutBusinessRules(
          {
            title: updateData.title ?? existingWorkout.title,
            startTime: updateData.startTime ? new Date(updateData.startTime) : existingWorkout.startTime,
            endTime: updateData.endTime ? new Date(updateData.endTime) : existingWorkout.endTime,
            minParticipants: updateData.minParticipants ?? existingWorkout.minParticipants,
            maxParticipants: updateData.maxParticipants ?? existingWorkout.maxParticipants,
          },
          {
            organizationId,
            workoutId: id, // Exclude current workout from conflict check
            existingWorkouts: allWorkouts.map((w) => ({
              id: w.id,
              organizationId: w.organizationId,
              title: w.title,
              description: w.description,
              startTime: w.startTime,
              endTime: w.endTime,
              minParticipants: w.minParticipants,
              maxParticipants: w.maxParticipants,
              status: w.status,
              location: w.location,
              createdAt: w.createdAt,
              updatedAt: w.updatedAt,
              participants: [], // Not needed for conflict checking
            })),
          }
        );

        if (!validation.valid) {
          return createErrorResponse('Bad Request', validation.message, 400);
        }
      }

      // Prepare update data (excluding status which is handled above)
      const updateValues: Partial<NewWorkout> = {
        updatedAt: new Date(),
      };

      if (updateData.title !== undefined) updateValues.title = updateData.title;
      if (updateData.description !== undefined) updateValues.description = updateData.description || null;
      if (updateData.startTime !== undefined) updateValues.startTime = new Date(updateData.startTime);
      if (updateData.endTime !== undefined) updateValues.endTime = new Date(updateData.endTime);
      if (updateData.minParticipants !== undefined) updateValues.minParticipants = updateData.minParticipants;
      if (updateData.maxParticipants !== undefined) updateValues.maxParticipants = updateData.maxParticipants;
      if (updateData.location !== undefined) updateValues.location = updateData.location || null;

      // Update workout (only if there are non-status fields to update)
      let updatedWorkout = existingWorkout;
      if (Object.keys(updateValues).length > 1) {
        // More than just updatedAt
        const result = await db.update(workouts).set(updateValues).where(eq(workouts.id, id)).returning();
        if (result[0]) {
          updatedWorkout = result[0];
        }
      } else {
        // If only status was updated, get the current workout data
        const result = await db.select().from(workouts).where(eq(workouts.id, id));
        if (result[0]) {
          updatedWorkout = result[0];
        }
      }

      // Get participants for response
      const participants = await db
        .select({
          id: workoutParticipants.id,
          customerId: workoutParticipants.customerId,
          customerName: users.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(members, eq(workoutParticipants.customerId, members.id))
        .innerJoin(users, eq(members.userId, users.id))
        .where(eq(workoutParticipants.workoutId, id));

      const response = {
        ...updatedWorkout,
        participants,
        participantCount: participants.length,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// DELETE /api/workouts/[id] - Delete workout (trainers/admins only)
export const DELETE = withTrainerAuth(
  async (_request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Check if workout exists and belongs to organization
      const [existingWorkout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.organizationId, organizationId)));

      if (!existingWorkout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Delete workout participants first (foreign key constraint)
      await db.delete(workoutParticipants).where(eq(workoutParticipants.workoutId, id));

      // Delete workout
      await db.delete(workouts).where(eq(workouts.id, id));

      return createSuccessResponse({ message: 'Workout deleted successfully' });
    } catch (error) {
      return handleApiError(error);
    }
  }
);
