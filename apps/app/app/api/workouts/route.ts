import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, members } from '@workspace/auth/server';
import { eq, and, ilike, asc, desc, count, gte, lte, sql } from 'drizzle-orm';
import {
  createWorkoutSchema,
  workoutQuerySchema,
  type PaginatedResponse,
  type WorkoutResponse,
} from '@/lib/validations';
import {
  // withAuth,
  withPublicAccess,
  withCustomerAuth,
  // withTrainerAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-utils';
import { validateWorkoutBusinessRules } from '@/lib/business-rules-service';

// GET / api / workouts - List workouts with search and pagination(public access)
export const GET = withPublicAccess(async (request: NextRequest, organizationId) => {
  try {
    const queryValidation = validateQueryParams(request, workoutQuerySchema);
    if (!queryValidation.success) {
      console.error('❌ queryValidation', queryValidation.error);
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    console.debug('✅ queryValidation', queryValidation.data);

    const { search, status, startDate, endDate, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(workouts.organizationId, organizationId)];

    if (search) {
      whereConditions.push(ilike(workouts.title, `%${search}%`));
    }

    if (status) {
      whereConditions.push(eq(workouts.status, status));
    }

    if (startDate) {
      whereConditions.push(gte(workouts.startTime, new Date(startDate)));
    }

    if (endDate) {
      whereConditions.push(lte(workouts.endTime, new Date(endDate)));
    }

    // Get total count
    const [totalResult] = await db
      .select({ total: count() })
      .from(workouts)
      .where(and(...whereConditions));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.total;

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'startTime';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      startTime: workouts.startTime,
      title: workouts.title,
      status: workouts.status,
      createdAt: workouts.createdAt,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || workouts.startTime;

    // Get workouts with participant count
    const workoutList = await db
      .select({
        id: workouts.id,
        organizationId: workouts.organizationId,
        title: workouts.title,
        description: workouts.description,
        startTime: workouts.startTime,
        endTime: workouts.endTime,
        minParticipants: workouts.minParticipants,
        maxParticipants: workouts.maxParticipants,
        status: workouts.status,
        location: workouts.location,
        createdAt: workouts.createdAt,
        updatedAt: workouts.updatedAt,
        participantCount: sql<number>`COALESCE(COUNT(${workoutParticipants.id}), 0)`,
      })
      .from(workouts)
      .leftJoin(workoutParticipants, eq(workouts.id, workoutParticipants.workoutId))
      .where(and(...whereConditions))
      .groupBy(workouts.id)
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 50)
      .offset(offset || 0);

    // Ensure we have valid limit and offset values
    const validLimit = limit || 50;
    const validOffset = offset || 0;

    const response: PaginatedResponse<WorkoutResponse> = {
      data: workoutList.map((workout) => ({
        ...workout,
        participantCount: Number(workout.participantCount),
      })),
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    console.debug('✅ Response', response);
    return createSuccessResponse(response);
  } catch (error) {
    console.error('Failed to fetch workouts:', error);
    return handleApiError(error);
  }
});

// POST /api/workouts - Create new workout (customers and trainers can create)
export const POST = withCustomerAuth(async (request: NextRequest, userSession) => {
  try {
    const organizationId = userSession.session.activeOrganizationId;
    if (!organizationId) {
      return createErrorResponse('Forbidden', 'No organization access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createWorkoutSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const workoutData = bodyValidation.data;

    // Validate business rules
    const validation = await validateWorkoutBusinessRules(
      {
        title: workoutData.title,
        startTime: new Date(workoutData.startTime),
        endTime: new Date(workoutData.endTime),
        minParticipants: workoutData.minParticipants ?? 1,
        maxParticipants: workoutData.maxParticipants ?? 5,
      },
      {
        organizationId,
      }
    );

    if (!validation.valid) {
      return createErrorResponse('Bad Request', validation.message, 400);
    }

    // Create workout
    const [newWorkout] = await db
      .insert(workouts)
      .values({
        organizationId,
        title: workoutData.title,
        description: workoutData.description || null,
        startTime: new Date(workoutData.startTime),
        endTime: new Date(workoutData.endTime),
        minParticipants: workoutData.minParticipants,
        maxParticipants: workoutData.maxParticipants,
        location: workoutData.location || null,
      })
      .returning();

    if (!newWorkout) {
      throw new Error('Failed to create workout');
    }

    // For customers, automatically assign themselves as the only participant
    const userId = userSession.user.id;
    const [customerMember] = await db
      .select({ id: members.id })
      .from(members)
      .where(and(
        eq(members.userId, userId), eq(members.organizationId, organizationId), eq(members.role, 'customer')
      ));

    if (customerMember) {
      await db
        .insert(workoutParticipants)
        .values({
          workoutId: newWorkout.id,
          customerId: customerMember.id,
          status: 'confirmed',
          enrolledAt: new Date(),
          confirmedAt: new Date(),
          creditDeducted: false, // Will be handled by the session status service
        });
    }

    const response: WorkoutResponse = {
      id: newWorkout.id,
      organizationId: newWorkout.organizationId,
      title: newWorkout.title,
      description: newWorkout.description,
      startTime: newWorkout.startTime,
      endTime: newWorkout.endTime,
      minParticipants: newWorkout.minParticipants,
      maxParticipants: newWorkout.maxParticipants,
      status: newWorkout.status,
      location: newWorkout.location,
      createdAt: newWorkout.createdAt,
      updatedAt: newWorkout.updatedAt,
      participants: customerMember ? [{
        id: customerMember.id,
        customerId: customerMember.id,
        status: 'confirmed',
        enrolledAt: new Date(),
        confirmedAt: new Date(),
        creditDeducted: false,
        customerName: userSession.user.name ?? '',
      }] : [],
      participantCount: customerMember ? 1 : 0,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    console.error('Failed to create workout:', error);
    return handleApiError(error);
  }
});
