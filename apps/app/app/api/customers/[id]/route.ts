import { NextRequest } from 'next/server';
import { db, members, users, type User } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateCustomerSchema, type CustomerResponse } from '@/lib/validations';
import {
  withAuth,
  withTrainer<PERSON><PERSON>,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
} from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';

// GET /api/customers/[id] - Get single customer
export const GET = withTrainerAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      const [customer] = await db
        .select({
          id: members.id,
          organizationId: members.organizationId,
          parentName: members.parentName,
          userId: members.userId,
          name: users.name,
          email: users.email,
          phone: users.phone,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(members)
        .innerJoin(users, eq(members.userId, users.id))
        .where(and(eq(members.id, id), eq(members.organizationId, organizationId), eq(members.role, 'customer')));

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(customer.id);

      const response: CustomerResponse = {
        id: customer.id,
        organizationId: customer.organizationId,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        totalSessions,
        parentName: customer.parentName,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// PUT /api/customers/[id] - Update customer
export const PUT = withTrainerAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      const bodyValidation = await validateRequestBody(request, updateCustomerSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const updateData = bodyValidation.data;

      // Check if customer exists and belongs to organization
      const [existingCustomer] = await db
        .select()
        .from(members)
        .where(and(eq(members.id, id), eq(members.organizationId, organizationId), eq(members.role, 'customer')));

      if (!existingCustomer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Update user fields
      const userUpdate: Partial<User> = { name: updateData.name, updatedAt: new Date() };
      if (typeof updateData.email !== 'undefined') {
        userUpdate.email = updateData.email;
      }

      if (typeof updateData.phone !== 'undefined') {
        userUpdate.phone = updateData.phone;
      }

      await db.update(users).set(userUpdate).where(eq(users.id, existingCustomer.userId));

      // Update customer-specific fields
      const [updatedCustomer] = await db
        .update(members)
        .set({
          parentName: updateData.parentName,
        })
        .where(eq(members.id, id))
        .returning();

      if (!updatedCustomer) {
        throw new Error('Failed to update customer');
      }

      // Get updated user info
      const [updatedUser] = await db.select().from(users).where(eq(users.id, existingCustomer.userId));
      const userInfo = updatedUser
        ? updatedUser
        : { name: '', email: '', phone: '', createdAt: new Date(), updatedAt: new Date() };

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(updatedCustomer.id);

      const response: CustomerResponse = {
        id: updatedCustomer.id,
        organizationId: updatedCustomer.organizationId,
        name: userInfo.name,
        email: userInfo.email,
        phone: userInfo.phone,
        totalSessions,
        parentName: updatedCustomer.parentName,
        createdAt: userInfo.createdAt,
        updatedAt: userInfo.updatedAt,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// DELETE /api/customers/[id] - Delete customer
export const DELETE = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const organizationId = user.session.activeOrganizationId;

    if (!organizationId) {
      return createErrorResponse('Forbidden', 'No organization access', 403);
    }

    // Check if customer exists and belongs to organization
    const [existingCustomer] = await db
      .select()
      .from(members)
      .where(and(eq(members.id, id), eq(members.organizationId, organizationId), eq(members.role, 'customer')));

    if (!existingCustomer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    // Delete customer member
    await db.delete(members).where(eq(members.id, id));

    return createSuccessResponse({ message: 'Customer deleted successfully' });
  } catch (error) {
    return handleApiError(error);
  }
});
