import { NextRequest } from 'next/server';
import { db, members, users } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { withAuth, createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';

// GET /api/customers/[id]/sessions - Get customer's total sessions
export const GET = withAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id: customerId } = await params;
      const organizationId = userSession.session.activeOrganizationId;

      if (!organizationId) {
        return createErrorResponse('Forbidden', 'No organization access', 403);
      }

      // Verify customer belongs to organization
      const [customer] = await db
        .select()
        .from(members)
        .innerJoin(users, eq(members.userId, users.id))
        .where(
          and(eq(members.id, customerId), eq(members.organizationId, organizationId), eq(members.role, 'customer'))
        );

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(customerId);

      const response = {
        customerId,
        totalSessions,
        customerName: customer.users.name,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);
