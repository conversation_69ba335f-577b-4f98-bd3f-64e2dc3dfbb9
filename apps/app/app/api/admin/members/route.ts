import { NextRequest } from 'next/server';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse,
  validateRequestBody,
  validateQueryParams,
} from '@/lib/api-utils';
import { auth, db, members, users } from '@workspace/auth/server';
import { and, asc, count, desc, eq, ilike } from 'drizzle-orm';
import {
  createMemberSchema,
  inviteMemberSchema,
  memberQuerySchema,
  MembersResponse,
  PaginatedResponse,
} from '@/lib/validations';

// GET /api/admin/members - Get all members of the active organization
export const GET = withAuth(async (request: NextRequest, userSession) => {
  try {
    const activeOrganizationId = userSession.session.activeOrganizationId;

    if (!activeOrganizationId) {
      console.error('❌ No active organization found in session');
      return createErrorResponse('Bad Request', 'No active organization found in session.', 400);
    }

    const queryValidation = validateQueryParams(request, memberQuerySchema);
    if (!queryValidation.success) {
      console.error('❌ Invalid query params', queryValidation.error);
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(members.organizationId, activeOrganizationId)];

    // Join user table for search
    if (search) {
      whereConditions.push(ilike(users.name, `%${search}%`));
    }

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'name';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      name: users.name,
      email: users.email,
      phone: users.phone,
      createdAt: users.createdAt,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || users.name;

    // Get total count for pagination
    const totalResult = await db
      .select({ total: count() })
      .from(members)
      .innerJoin(users, eq(members.userId, users.id))
      .where(and(...whereConditions));

    const total = totalResult[0]?.total || 0;

    // Get paginated results
    const membersList = await db
      .select({
        id: members.id,
        userId: members.userId,
        organizationId: members.organizationId,
        role: members.role,
        name: users.name,
        email: users.email,
        phone: users.phone,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(members)
      .innerJoin(users, eq(members.userId, users.id))
      .where(and(...whereConditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    const response: PaginatedResponse<MembersResponse> = {
      data: membersList,
      pagination: {
        total,
        limit: limit || 20,
        offset: offset || 0,
        hasMore: (offset || 0) + (limit || 20) < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    console.error('❌ Failed to fetch members', error);
    return createErrorResponse('Internal Server Error', 'Failed to fetch members', 500);
  }
});

// POST /api/admin/members - Invite or add a new member
export const POST = withAuth(async (request: NextRequest, userSession) => {
  const activeOrganizationId = userSession.session.activeOrganizationId;

  if (!activeOrganizationId) {
    return createErrorResponse('Bad Request', 'No active organization found in session.', 400);
  }

  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  if (action === 'invite') {
    const bodyValidation = await validateRequestBody(request, inviteMemberSchema);
    if (!bodyValidation.success) {
      return createErrorResponse('Validation Error', 'Invalid request body', 400, bodyValidation.error.details);
    }

    const { email } = bodyValidation.data;

    try {
      const invitation = await auth.api.createInvitation({
        body: { organizationId: activeOrganizationId, email, role: 'member' },
      });

      return createSuccessResponse(invitation, 201);
    } catch (error) {
      console.error('❌ Failed to invite member', error);
      return createErrorResponse('Internal Server Error', 'Failed to invite member', 500);
    }
  } else {
    const bodyValidation = await validateRequestBody(request, createMemberSchema);
    if (!bodyValidation.success) {
      return createErrorResponse('Validation Error', 'Invalid request body', 400, bodyValidation.error.details);
    }

    const { name, email, phone } = bodyValidation.data;

    try {
      // Create the user with the 'trainer' role
      // const { user: newTrainerUser } = await auth.api.
      // if (!newTrainerUser) {
      //   throw new Error('Failed to create trainer user');
      // }
      // // Add the new user to the active organization
      // await auth.api.addMember({
      //   body: { organizationId: activeOrganizationId, userId: newTrainerUser.id, role: 'trainer' },
      // });
      // Refresh permissions for the new member
      // await refreshUserPermissions(newTrainerUser.id);
      // return createSuccessResponse({ message: 'Trainer added successfully', user: newTrainerUser }, 201);
    } catch (error) {
      console.error('❌ Failed to add trainer', error);
      return createErrorResponse('Internal Server Error', 'Failed to add trainer', 500);
    }
  }

  return createErrorResponse('Bad Request', 'Invalid action specified', 400);
});
