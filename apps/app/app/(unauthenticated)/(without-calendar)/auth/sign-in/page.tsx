import Link from 'next/link';
import Image from 'next/image';
import AuthUsernameCard from './view';

export default async function AuthPage() {
  return (
    <>
      <main className="max-w-xl min-h-svh flex grow flex-col items-center justify-center gap-3 self-center p-4 md:p-6 mx-auto">
        <AuthUsernameCard />
      </main>

      <div className="absolute left-5 top-[20.5px]">
        <Link href="/">
          <span className="sr-only">Logo</span>
          <Image src="/logo.png" alt="Logo" width={32} height={32} />
        </Link>
      </div>
    </>
  );
}
