'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  // CardFooter,
} from '@/components/ui/card';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { AuthUIContext, authClient } from '@workspace/auth';
import { Loader2 } from 'lucide-react';
// import Link from 'next/link';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import z from 'zod';
import { useOnSuccessTransition } from './hooks';
import { cacheUserRole } from './actions';

export default function AuthUsernameCard() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { redirectTo } = useContext(AuthUIContext);

  useEffect(() => {
    const handlePageHide = () => {
      setIsSubmitting(false);
    };

    window.addEventListener('pagehide', handlePageHide);

    return () => {
      setIsSubmitting(false);
      window.removeEventListener('pagehide', handlePageHide);
    };
  }, []);

  return (
    <Card className={cn('w-full max-w-sm')}>
      <CardHeader>
        <CardTitle className={cn('text-lg md:text-xl')}>Sign In</CardTitle>

        <CardDescription className={cn('text-xs md:text-sm')}>
          Enter your username below to login to your account
        </CardDescription>
      </CardHeader>

      <CardContent className={cn('grid gap-6')}>
        <div className="grid gap-4">
          <SignInForm isSubmitting={isSubmitting} setIsSubmitting={setIsSubmitting} redirectTo={redirectTo} />
        </div>
      </CardContent>

      {/* <CardFooter className={cn('justify-center gap-1.5 text-muted-foreground text-sm')}>
        Don\'t have an account
        <Link className={cn('text-foreground underline')} href="/auth/sign-up">
          <Button variant="link" size="sm" className={cn('px-0 text-foreground underline')}>
            Sign Up
          </Button>
        </Link>
      </CardFooter> */}
    </Card>
  );
}

export function SignInForm({
  isSubmitting,
  setIsSubmitting,
  redirectTo,
}: {
  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;
  redirectTo?: string;
}) {
  //   const isHydrated = useIsHydrated();
  //   const { captchaRef, getCaptchaHeaders } = useCaptcha({ localization });

  const { onSuccess, isPending: transitionPending } = useOnSuccessTransition({
    redirectTo,
  });

  const formSchema = z.object({
    username: z.string().min(1, {
      message: `Username is required`,
    }),
    // password: getPasswordSchema(passwordValidation, localization),
    rememberMe: z.boolean().optional(),
  });

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      //   password: '',
      //   rememberMe: !rememberMeEnabled,
    },
  });

  isSubmitting = isSubmitting || form.formState.isSubmitting; // || transitionPending;

  useEffect(() => {
    setIsSubmitting?.(form.formState.isSubmitting || transitionPending);
  }, [form.formState.isSubmitting, transitionPending, setIsSubmitting]);

  async function signIn({
    username,
    // password,
    // rememberMe,
  }: z.infer<typeof formSchema>) {
    try {
      let response: Record<string, any> = {};

      // const fetchOptions = {
      //   throw: true,
      //   headers: await getCaptchaHeaders('/sign-in/username'),
      // };

      response = await authClient.signIn.username({
        username,
        password: 'abcd1234',
        // rememberMe,
        // fetchOptions,
      });

      if (!response.data?.user?.id) {
        throw new Error('User not found');
      }

      console.debug('✅ Sign in response', response);

      await cacheUserRole(response.data?.user.id);
      await onSuccess();
    } catch (error) {
      console.error('❌ Sign in error', error);

      toast.error(typeof error === 'string' ? error : 'Failed to sign in');
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(signIn)} noValidate className="grid w-full gap-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>

              <FormControl>
                <Input autoComplete="username" type="text" placeholder="Username" disabled={isSubmitting} {...field} />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center justify-between">
                <FormLabel className={classNames?.label}>{localization.PASSWORD}</FormLabel>

                {credentials?.forgotPassword && (
                  <Link
                    className={cn('text-sm hover:underline', classNames?.forgotPasswordLink)}
                    href={`${basePath}/${viewPaths.FORGOT_PASSWORD}${isHydrated ? window.location.search : ''}`}
                  >
                    {localization.FORGOT_PASSWORD_LINK}
                  </Link>
                )}
              </div>

              <FormControl>
                <PasswordInput
                  autoComplete="current-password"
                  className={classNames?.input}
                  placeholder={localization.PASSWORD_PLACEHOLDER}
                  disabled={isSubmitting}
                  {...field}
                />
              </FormControl>

              <FormMessage className={classNames?.error} />
            </FormItem>
          )}
        /> */}

        {/* {rememberMeEnabled && (
          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex">
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={isSubmitting} />
                </FormControl>

                <FormLabel>{localization.REMEMBER_ME}</FormLabel>
              </FormItem>
            )}
          />
        )} */}

        {/* <Captcha ref={captchaRef} localization={localization} action="/sign-in/email" /> */}

        <Button type="submit" disabled={isSubmitting} className={cn('w-full')}>
          {isSubmitting ? <Loader2 className="animate-spin" /> : 'Login'}
        </Button>
      </form>
    </Form>
  );
}
