import { ReactNode } from 'react';
import { CalendarProvider } from '@/components/event-calendar/calendar-context';
import { AppPermissionProvider } from '@/components/permission-provider';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { CalendarPageWrapper } from '@/components/calendar-page-wrapper';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';
import { getAuthenticatedUser } from '@/lib/api-utils';
import { headers } from 'next/headers';
import {
  canAccessFinancial,
  canReadPackages,
  canAccessOrganization,
  canManageCustomers,
  canManageWorkouts,
} from '@/lib/permission-utils';

export default async function PublicLayout({ children }: { children: ReactNode }) {
  let hasFinancialPermission = false;
  let hasPackagePermission = false;
  let hasOrganizationPermission = false;
  let hasCustomerPermission = false;
  let hasWorkoutPermission = false;

  const userSession = await getAuthenticatedUser(undefined, await headers());

  if (userSession) {
    hasFinancialPermission = await canAccessFinancial(userSession.user.id);
    hasPackagePermission = await canReadPackages(userSession.user.id);
    hasOrganizationPermission = await canAccessOrganization(userSession.user.id);
    hasCustomerPermission = await canManageCustomers(userSession.user.id);
    hasWorkoutPermission = await canManageWorkouts(userSession.user.id);
  }

  return (
    <>
      <AppPermissionProvider
        value={{
          hasFinancialPermission,
          hasPackagePermission,
          hasOrganizationPermission,
          hasCustomerPermission,
          hasWorkoutPermission,
        }}
      >
        <CalendarProvider>
          <SidebarProvider>
            <CalendarPageWrapper>
              <AppSidebar />
              <SidebarInset>
                <div className="flex flex-1 flex-col gap-4 p-2 pt-0">{children}</div>
              </SidebarInset>
              {hasCustomerPermission && <CustomerSidebar />}
            </CalendarPageWrapper>
          </SidebarProvider>
        </CalendarProvider>
      </AppPermissionProvider>
    </>
  );
}
