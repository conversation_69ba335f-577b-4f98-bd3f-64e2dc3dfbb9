# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development

- `pnpm dev` - Start development server with Turbo
- `pnpm build` - Build all packages and apps
- `pnpm lint` - Run linting across all packages
- `pnpm format` - Format all code with Prettier

### App-specific (from `/apps/app`)

- `pnpm dev` - Start Next.js dev server with Turbopack
- `pnpm build` - Build Next.js app
- `pnpm start` - Start production server
- `pnpm lint` - Run Next.js linting
- `pnpm registry:build` - Build shadcn/ui registry

### Testing

- Run individual tests by navigating to specific component directories and using standard Next.js/React testing patterns

## Architecture Overview

### Multi-tenant SaaS Structure

This is a multi-tenant gym management SaaS application built with Next.js 15, TypeScript, and Turbo monorepo architecture.

### Key Architecture Components

**Multi-tenancy**: Uses subdomain-based organization isolation (`tenant.example.com`) with middleware extracting subdomains into `X-Organization` headers for API routes.

**Authentication**: Uses Better Auth with role-based permissions (admin/trainer/customer) cached in cookies for 15-minute intervals.

**Routing Structure**:

- `(authenticated)/(with-organization)/` - Protected routes requiring auth + organization
- `(authenticated)/(without-organization)/onboarding/` - Onboarding flow for new trainers
- `(unauthenticated)/auth/[pathname]/` - Auth pages (sign-in, sign-up)

**Permission System**: Role-based access control with specific permissions:

- Admin: Full access (financial, organization, packages, customers, workouts)
- Trainer: Customer and workout management
- Customer: Basic access

**Calendar System**: Event-based calendar with drag-and-drop functionality using @dnd-kit, supporting day/week/month views with customer session scheduling.

**Package Management**: Gym packages for customer purchases with credit tracking system.

### Key Directories

- `/apps/app/` - Main Next.js application
- `/apps/app/components/` - UI components organized by feature (admin, customers, workouts, calendar)
- `/apps/app/lib/` - Business logic (permissions, credit service, package service)
- `/apps/app/api/` - API routes for customers, packages, workouts, trainer onboarding
- `/apps/app/hooks/` - React hooks for data fetching (SWR-based)

### Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS v4
- **UI**: shadcn/ui components, Radix UI, Framer Motion
- **State**: SWR for data fetching, React hooks for local state
- **Styling**: Tailwind CSS with custom components
- **Database**: Drizzle ORM (implied from dependencies)
- **Auth**: Better Auth with organization support
